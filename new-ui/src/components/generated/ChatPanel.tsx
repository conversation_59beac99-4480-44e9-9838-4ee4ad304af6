import React, { useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '../../lib/utils';
interface Message {
  id: string;
  content: string;
  sender: 'user' | 'agent';
  timestamp: Date;
}
interface ChatPanelProps {
  messages: Message[];
  isTyping?: boolean;
  className?: string;
}
const ChatPanel: React.FC<ChatPanelProps> = ({
  messages,
  isTyping = false,
  className
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({
      behavior: 'smooth'
    });
  };
  useEffect(() => {
    scrollToBottom();
  }, [messages]);
  return <div ref={containerRef} className={cn("flex-1 overflow-y-auto p-6 space-y-6", className)}>
      <AnimatePresence mode="popLayout">
        {messages.map((message, index) => <motion.div key={message.id} initial={{
        opacity: 0,
        y: 20,
        scale: 0.95
      }} animate={{
        opacity: 1,
        y: 0,
        scale: 1
      }} exit={{
        opacity: 0,
        y: -20,
        scale: 0.95
      }} transition={{
        duration: 0.3,
        delay: index * 0.05,
        ease: 'easeOut'
      }} className={cn("flex", message.sender === 'user' ? 'justify-end' : 'justify-start')}>
            <div className={cn("flex items-start space-x-3 max-w-2xl", message.sender === 'user' ? 'flex-row-reverse space-x-reverse' : '')}>
              {/* Avatar */}
              <div className={cn("flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium", message.sender === 'user' ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground")}>
                {message.sender === 'user' ? 'U' : 'AI'}
              </div>

              {/* Message Bubble */}
              <div className={cn("relative px-4 py-3 rounded-2xl shadow-sm", message.sender === 'user' ? "bg-primary text-primary-foreground rounded-br-md" : "bg-card border border-border text-foreground rounded-bl-md")}>
                <p className="text-sm leading-relaxed whitespace-pre-wrap">
                  {message.content}
                </p>
                
                {/* Timestamp */}
                <time className={cn("text-xs mt-2 block", message.sender === 'user' ? "opacity-80" : "text-muted-foreground")}>
                  {message.timestamp.toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit'
              })}
                </time>

                {/* Message tail */}
                <div className={cn("absolute w-3 h-3 transform rotate-45", message.sender === 'user' ? "bg-primary -bottom-1 right-3" : "bg-card border-r border-b border-border -bottom-1 left-3")} />
              </div>
            </div>
          </motion.div>)}
      </AnimatePresence>

      {/* Typing Indicator */}
      <AnimatePresence>
        {isTyping && <motion.div initial={{
        opacity: 0,
        y: 20
      }} animate={{
        opacity: 1,
        y: 0
      }} exit={{
        opacity: 0,
        y: -20
      }} className="flex justify-start">
            <div className="flex items-start space-x-3 max-w-2xl">
              <div className="flex-shrink-0 w-8 h-8 rounded-full bg-muted text-muted-foreground flex items-center justify-center text-xs font-medium">
                AI
              </div>
              <div className="relative px-4 py-3 bg-card border border-border rounded-2xl rounded-bl-md">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{
                animationDelay: '0ms'
              }} />
                  <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{
                animationDelay: '150ms'
              }} />
                  <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{
                animationDelay: '300ms'
              }} />
                </div>
                <div className="absolute w-3 h-3 bg-card border-r border-b border-border transform rotate-45 -bottom-1 left-3" />
              </div>
            </div>
          </motion.div>}
      </AnimatePresence>

      {/* Empty State */}
      {messages.length === 0 && !isTyping && <motion.div initial={{
      opacity: 0,
      y: 20
    }} animate={{
      opacity: 1,
      y: 0
    }} className="flex flex-col items-center justify-center h-full text-center py-12">
          <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
            <span className="text-2xl">🤖</span>
          </div>
          <h3 className="text-lg font-medium text-foreground mb-2">
            Start a conversation
          </h3>
          <p className="text-sm text-muted-foreground max-w-md">
            Ask me anything! I'm here to help with your questions and tasks.
          </p>
        </motion.div>}

      {/* Scroll anchor */}
      <div ref={messagesEndRef} />
    </div>;
};
export default ChatPanel;