import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../lib/utils';
interface Conversation {
  id: string;
  title: string;
  lastMessage: string;
  timestamp: Date;
  isActive?: boolean;
}
interface ConversationListProps {
  conversations: Conversation[];
  activeConversation: string;
  onConversationSelect: (id: string) => void;
  className?: string;
}
const ConversationList: React.FC<ConversationListProps> = ({
  conversations,
  activeConversation,
  onConversationSelect,
  className
}) => {
  return <div className={cn("flex-1 overflow-y-auto", className)}>
      <h2 className="text-sm font-medium text-muted-foreground mb-3 px-6">Recent Conversations</h2>
      <div className="space-y-2 px-6">
        {conversations.map((conversation, index) => <motion.button key={conversation.id} initial={{
        opacity: 0,
        y: 20
      }} animate={{
        opacity: 1,
        y: 0
      }} transition={{
        delay: index * 0.05
      }} onClick={() => onConversationSelect(conversation.id)} className={cn("w-full text-left p-3 rounded-lg transition-all duration-200 group", "hover:shadow-sm hover:scale-[1.02]", activeConversation === conversation.id ? "bg-primary text-primary-foreground shadow-md" : "hover:bg-accent text-foreground")}>
            <div className="flex items-start justify-between mb-1">
              <h3 className="font-medium text-sm truncate pr-2 flex-1">
                {conversation.title}
              </h3>
              <div className={cn("w-2 h-2 rounded-full transition-opacity", activeConversation === conversation.id ? "bg-primary-foreground opacity-100" : "bg-muted-foreground opacity-0 group-hover:opacity-50")} />
            </div>
            <p className={cn("text-xs truncate mb-2", activeConversation === conversation.id ? "opacity-90" : "opacity-70")}>
              {conversation.lastMessage}
            </p>
            <div className="flex items-center justify-between">
              <time className={cn("text-xs", activeConversation === conversation.id ? "opacity-80" : "opacity-60")}>
                {conversation.timestamp.toLocaleTimeString([], {
              hour: '2-digit',
              minute: '2-digit'
            })}
              </time>
              {conversation.isActive && <div className="flex items-center space-x-1">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse" />
                  <span className="text-xs opacity-70">Active</span>
                </div>}
            </div>
          </motion.button>)}
      </div>
      
      {conversations.length === 0 && <div className="px-6 py-8 text-center">
          <p className="text-sm text-muted-foreground">No conversations yet</p>
          <p className="text-xs text-muted-foreground mt-1">Start a new chat to begin</p>
        </div>}
    </div>;
};
export default ConversationList;