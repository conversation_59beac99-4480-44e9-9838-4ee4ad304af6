import React from 'react';
import { motion } from 'framer-motion';
import { MessageSquare, Settings, LogOut, ChevronRight } from 'lucide-react';
import { cn } from '../../lib/utils';
interface Conversation {
  id: string;
  title: string;
  lastMessage: string;
  timestamp: Date;
  isActive?: boolean;
}
interface SidebarProps {
  isOpen: boolean;
  conversations: Conversation[];
  activeConversation: string;
  onConversationSelect: (id: string) => void;
  onSettingsClick: () => void;
  onSignOutClick: () => void;
}
const Sidebar: React.FC<SidebarProps> = ({
  isOpen,
  conversations,
  activeConversation,
  onConversationSelect,
  onSettingsClick,
  onSignOutClick
}) => {
  return <motion.aside initial={false} animate={{
    width: isOpen ? 320 : 0
  }} transition={{
    duration: 0.3,
    ease: 'easeInOut'
  }} className="relative bg-card border-r border-border overflow-hidden">
      <div className="flex flex-col h-full">
        {/* Sidebar Header */}
        <header className="p-6 border-b border-border">
          <nav className="flex items-center text-sm text-muted-foreground mb-4">
            <span>Dashboard</span>
            <ChevronRight className="w-4 h-4 mx-2" />
            <span className="text-foreground">Agent Chat</span>
          </nav>
          <h1 className="text-xl font-semibold text-foreground">AI Assistant</h1>
        </header>

        {/* Navigation */}
        <nav className="px-6 py-4 border-b border-border">
          <button className="flex items-center w-full px-3 py-2 text-sm text-muted-foreground hover:text-foreground hover:bg-accent rounded-md transition-colors">
            <MessageSquare className="w-4 h-4 mr-3" />
            Browse Conversations
          </button>
        </nav>

        {/* Conversations List */}
        <div className="flex-1 overflow-y-auto px-6 py-4">
          <h2 className="text-sm font-medium text-muted-foreground mb-3">Recent Conversations</h2>
          <div className="space-y-2">
            {conversations.map(conversation => <button key={conversation.id} onClick={() => onConversationSelect(conversation.id)} className={cn("w-full text-left p-3 rounded-lg transition-colors", activeConversation === conversation.id ? "bg-primary text-primary-foreground" : "hover:bg-accent text-foreground")}>
                <h3 className="font-medium text-sm mb-1 truncate">{conversation.title}</h3>
                <p className="text-xs opacity-80 truncate">{conversation.lastMessage}</p>
                <time className="text-xs opacity-60 mt-1 block">
                  {conversation.timestamp.toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit'
              })}
                </time>
              </button>)}
          </div>
        </div>

        {/* Sidebar Footer */}
        <footer className="p-6 border-t border-border space-y-2">
          <button onClick={onSettingsClick} className="flex items-center w-full px-3 py-2 text-sm text-muted-foreground hover:text-foreground hover:bg-accent rounded-md transition-colors">
            <Settings className="w-4 h-4 mr-3" />
            Settings
          </button>
          <button onClick={onSignOutClick} className="flex items-center w-full px-3 py-2 text-sm text-muted-foreground hover:text-foreground hover:bg-accent rounded-md transition-colors">
            <LogOut className="w-4 h-4 mr-3" />
            Sign Out
          </button>
        </footer>
      </div>
    </motion.aside>;
};
export default Sidebar;