"use client";

import React, { useState } from 'react';
import { Search, Grid3X3, User, Zap, HelpCircle, Calendar, RotateCcw, Send, Paperclip, Mic, MessageSquare, ThumbsUp, ThumbsDown, Copy, Share, ChevronDown } from 'lucide-react';
import { cn } from '../../lib/utils';
interface Message {
  id: string;
  content: string;
  sender: 'user' | 'agent';
  timestamp: Date;
}
interface Project {
  id: string;
  title: string;
  description: string;
}
const AgentPage: React.FC = () => {
  const [inputValue, setInputValue] = useState('');
  const [selectedOption, setSelectedOption] = useState('Executive Summary');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([{
    id: '1',
    content: 'Generate 5 attention-grabbing headlines for an article about AI Chat Copywriter',
    sender: 'user',
    timestamp: new Date()
  }, {
    id: '2',
    content: `Here's the results of 5 attention-grabbing headlines:

1. "Revolutionize Customer Engagement with AI Chat Copywriter"

2. "Unleash the Power of AI Chat Copywriters for Transformative Customer Experiences"

3. "Chatbots on Steroids: Meet the AI Copywriter Transforming Conversations"

4. "From Bland to Brilliant: AI Chat Copywriters Make Brands Conversational Rockstars"

5. "Say Goodbye to Boring Chats: AI Copywriters Elevate Conversational Marketing"`,
    sender: 'agent',
    timestamp: new Date()
  }]);
  const projects: Project[] = [{
    id: '1',
    title: 'Generate 5 attention-grab...',
    description: '"Revolutionize Customer Enga...'
  }, {
    id: '2',
    title: 'Learning From 100 Years o...',
    description: 'For athletes, high altitude prod...'
  }, {
    id: '3',
    title: 'Research officiants',
    description: "Maxwell's equations—the foun..."
  }, {
    id: '4',
    title: 'What does a senior lead de...',
    description: 'Physiological respiration involv...'
  }, {
    id: '5',
    title: 'Write a sweet note to your...',
    description: 'In the eighteenth century the G...'
  }, {
    id: '6',
    title: 'Meet with cake bakers',
    description: 'Physical space is often conceiv...'
  }, {
    id: '7',
    title: 'Meet with cake bakers',
    description: 'Physical space is often conceiv...'
  }];
  const handleSendMessage = () => {
    if (!inputValue.trim()) return;
    const newMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      sender: 'user',
      timestamp: new Date()
    };
    setMessages(prev => [...prev, newMessage]);
    setInputValue('');

    // Simulate agent response
    setTimeout(() => {
      const agentResponse: Message = {
        id: (Date.now() + 1).toString(),
        content: 'I understand your request. Let me help you with that.',
        sender: 'agent',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, agentResponse]);
    }, 1000);
  };
  return <div className="flex h-screen bg-white text-gray-900">
      {/* Left Sidebar */}
      <aside className="w-64 bg-white border-r border-gray-200 flex flex-col">
        {/* Header */}
        <header className="p-4 border-b border-gray-200">
          <div className="flex items-center space-x-2 mb-4">
            <div className="w-6 h-6 bg-black rounded-sm flex items-center justify-center">
              <Grid3X3 className="w-4 h-4 text-white" />
            </div>
            <span className="font-semibold text-lg">Robynn AI</span>
          </div>
          
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input type="text" placeholder="Search" className="w-full pl-10 pr-8 py-2 bg-gray-100 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" />
            <kbd className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-gray-400">⌘K</kbd>
          </div>
        </header>

        {/* Navigation */}
        <nav className="flex-1 p-4">
          <div className="space-y-1">
            <button className="flex items-center w-full px-3 py-2 text-sm text-white bg-blue-600 rounded-lg" style={{
            background: "#7c3aed"
          }}>
              <MessageSquare className="w-4 h-4 mr-3" />
              AI Chat
            </button>
            <button className="flex items-center w-full px-3 py-2 text-sm text-gray-600 hover:bg-gray-100 rounded-lg">
              <Grid3X3 className="w-4 h-4 mr-3" />
              Projects
            </button>
            <button className="flex items-center w-full px-3 py-2 text-sm text-gray-600 hover:bg-gray-100 rounded-lg">
              <Grid3X3 className="w-4 h-4 mr-3" />
              Templates
            </button>
            <button className="flex items-center w-full px-3 py-2 text-sm text-gray-600 hover:bg-gray-100 rounded-lg">
              <Grid3X3 className="w-4 h-4 mr-3" />
              Documents
            </button>
            <button className="flex items-center w-full px-3 py-2 text-sm text-gray-600 hover:bg-gray-100 rounded-lg">
              <User className="w-4 h-4 mr-3" />
              Community
              <span className="ml-auto bg-blue-600 text-white text-xs px-2 py-0.5 rounded-full">NEW</span>
            </button>
            <button className="flex items-center w-full px-3 py-2 text-sm text-gray-600 hover:bg-gray-100 rounded-lg">
              <Calendar className="w-4 h-4 mr-3" />
              History
            </button>
          </div>
        </nav>

        {/* Footer */}
        <footer className="p-4 border-t border-gray-200">
          <div className="space-y-1">
            <button className="flex items-center w-full px-3 py-2 text-sm text-gray-600 hover:bg-gray-100 rounded-lg">
              <Grid3X3 className="w-4 h-4 mr-3" />
              Settings
            </button>
            <button className="flex items-center w-full px-3 py-2 text-sm text-gray-600 hover:bg-gray-100 rounded-lg" style={{
            display: "none"
          }}>
              <HelpCircle className="w-4 h-4 mr-3" />
              Help
            </button>
          </div>
          
          {/* Theme Toggle */}
          <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-200" style={{
          display: "none"
        }}>
            <button className="flex items-center text-sm text-gray-600">
              <div className="w-4 h-4 mr-2 rounded-full bg-yellow-400"></div>
              Light
            </button>
            <button className="flex items-center text-sm text-gray-400">
              <div className="w-4 h-4 mr-2 rounded-full bg-gray-800"></div>
              Dark
            </button>
          </div>

          {/* User Profile */}
          <div className="flex items-center mt-4 pt-4 border-t border-gray-200">
            <div className="w-8 h-8 bg-gray-300 rounded-full mr-3"></div>
            <div>
              <p className="text-sm font-medium">Emilia Caitlin</p>
              <p className="text-xs text-gray-500"><EMAIL></p>
            </div>
          </div>
        </footer>
      </aside>

      {/* Main Content */}
      <main className="flex-1 flex flex-col">
        {/* Header */}
        <header className="flex items-center justify-between p-6 border-b border-gray-200 bg-white">
          <div className="flex items-center">
            <h1 className="text-xl font-semibold mr-4">SEO Agent</h1>
          </div>
          <div className="flex items-center space-x-4" style={{
          display: "none"
        }}>
            <button className="bg-black text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center">
              <Zap className="w-4 h-4 mr-2" />
              Upgrade
            </button>
            <button className="p-2 hover:bg-gray-100 rounded-lg">
              <HelpCircle className="w-5 h-5 text-gray-600" />
            </button>
            <button className="p-2 hover:bg-gray-100 rounded-lg">
              <Grid3X3 className="w-5 h-5 text-gray-600" />
            </button>
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-black rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">EC</span>
              </div>
            </div>
          </div>
        </header>

        <div className="flex flex-1">
          {/* Chat Area */}
          <div className="flex-1 flex flex-col">
            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-6">
              <div className="max-w-4xl mx-auto space-y-6">
                {messages.map(message => <div key={message.id} className="flex items-start space-x-4">
                    <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center flex-shrink-0">
                      {message.sender === 'user' ? <User className="w-4 h-4 text-gray-600" /> : <div className="w-4 h-4 bg-black rounded-sm"></div>}
                    </div>
                    <div className="flex-1">
                      <p className="text-gray-900 whitespace-pre-wrap">{message.content}</p>
                      {message.sender === 'agent' && <div className="flex items-center space-x-2 mt-4">
                          <button className="p-1 hover:bg-gray-100 rounded">
                            <ThumbsUp className="w-4 h-4 text-gray-500" />
                          </button>
                          <button className="p-1 hover:bg-gray-100 rounded">
                            <ThumbsDown className="w-4 h-4 text-gray-500" />
                          </button>
                          <button className="flex items-center px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded-lg">
                            <Copy className="w-4 h-4 mr-1" />
                            Copy
                          </button>
                          <button className="flex items-center px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded-lg">
                            <Share className="w-4 h-4 mr-1" />
                            Share
                          </button>
                        </div>}
                    </div>
                  </div>)}
              </div>

              {/* Regenerate Button */}
              <div className="max-w-4xl mx-auto mt-6">
                <button className="flex items-center px-4 py-2 text-sm text-gray-600 hover:bg-gray-100 rounded-lg border border-gray-200">
                  <RotateCcw className="w-4 h-4 mr-2" />
                  Regenerate
                </button>
              </div>
            </div>

            {/* Input Area */}
            <div className="p-6 border-t border-gray-200 bg-white">
              <div className="max-w-4xl mx-auto">
                <div className="relative">
                  <textarea value={inputValue} onChange={e => setInputValue(e.target.value)} onKeyDown={e => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSendMessage();
                  }
                }} placeholder="Send a message" className="w-full px-4 py-3 pr-32 border border-gray-200 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" rows={1} style={{
                  minHeight: '48px',
                  maxHeight: '120px'
                }} />
                  <div className="absolute right-2 bottom-2 flex items-center space-x-2">
                    <button className="p-2 hover:bg-gray-100 rounded-lg">
                      <Paperclip className="w-4 h-4 text-gray-500" />
                    </button>
                    <button className="p-2 hover:bg-gray-100 rounded-lg">
                      <Mic className="w-4 h-4 text-gray-500" />
                    </button>
                    <button onClick={handleSendMessage} disabled={!inputValue.trim()} className="p-2 bg-gray-900 text-white rounded-lg hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed">
                      <Send className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center space-x-4 mt-3">
                  <button className="flex items-center px-3 py-1.5 text-sm text-gray-600 hover:bg-gray-100 rounded-lg border border-gray-200">
                    <Paperclip className="w-4 h-4 mr-1" />
                    Attach
                  </button>
                  <button className="flex items-center px-3 py-1.5 text-sm text-gray-600 hover:bg-gray-100 rounded-lg border border-gray-200">
                    <Mic className="w-4 h-4 mr-1" />
                    Voice Message
                  </button>
                  <button className="flex items-center px-3 py-1.5 text-sm text-white bg-blue-600 hover:bg-blue-700 rounded-lg border border-blue-600 transition-colors" style={{
                  background: "#7c3aed"
                }}>
                    <MessageSquare className="w-4 h-4 mr-1" />
                    Browse Prompts
                  </button>
                  <div className="ml-auto text-xs text-gray-400">
                    0 / 3,000
                  </div>
                </div>

                {/* Disclaimer */}
                <p className="text-xs text-gray-400 text-center mt-4">
                  Script may generate inaccurate information about people, places, or facts. Model: Script AI v1.3
                </p>
              </div>
            </div>
          </div>

          {/* Right Sidebar - Projects */}
          <aside className="w-80 bg-white border-l border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold">Projects (7)</h2>
              <button className="text-gray-400 hover:text-gray-600">
                <Grid3X3 className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              {projects.map(project => <div key={project.id} className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                  <h3 className="font-medium text-sm mb-2">{project.title}</h3>
                  <p className="text-xs text-gray-500">{project.description}</p>
                </div>)}
            </div>
          </aside>
        </div>
      </main>
    </div>;
};
export default AgentPage;