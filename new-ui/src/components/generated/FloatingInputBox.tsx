import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Send, Spark<PERSON>, Paperclip, Mic, Square } from 'lucide-react';
import { cn } from '../../lib/utils';
interface FloatingInputBoxProps {
  value: string;
  onChange: (value: string) => void;
  onSend: () => void;
  onPromptSelect: (prompt: string) => void;
  promptChoices?: string[];
  isLoading?: boolean;
  placeholder?: string;
  className?: string;
}
const FloatingInputBox: React.FC<FloatingInputBoxProps> = ({
  value,
  onChange,
  onSend,
  onPromptSelect,
  promptChoices = [],
  isLoading = false,
  placeholder = "Type your message...",
  className
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [showPrompts, setShowPrompts] = useState(true);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
    }
  }, [value]);

  // Hide prompts when user starts typing
  useEffect(() => {
    setShowPrompts(value.length === 0);
  }, [value]);
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (value.trim() && !isLoading) {
        onSend();
      }
    }
  };
  const handlePromptClick = (prompt: string) => {
    onPromptSelect(prompt);
    setShowPrompts(false);
  };
  const toggleRecording = () => {
    setIsRecording(!isRecording);
    // Here you would implement actual voice recording logic
  };
  return <div className={cn("p-6 bg-background/95 backdrop-blur-sm border-t border-border", className)}>
      {/* Prompt Choices */}
      <AnimatePresence>
        {showPrompts && promptChoices.length > 0 && <motion.div initial={{
        opacity: 0,
        y: 10,
        height: 0
      }} animate={{
        opacity: 1,
        y: 0,
        height: 'auto'
      }} exit={{
        opacity: 0,
        y: -10,
        height: 0
      }} transition={{
        duration: 0.2
      }} className="mb-4 overflow-hidden">
            <div className="flex flex-wrap gap-2">
              {promptChoices.map((choice, index) => <motion.button key={index} initial={{
            opacity: 0,
            scale: 0.9
          }} animate={{
            opacity: 1,
            scale: 1
          }} transition={{
            delay: index * 0.05
          }} onClick={() => handlePromptClick(choice)} className="group px-3 py-2 text-sm bg-accent hover:bg-accent/80 text-accent-foreground rounded-full transition-all duration-200 flex items-center hover:shadow-sm hover:scale-105">
                  <Sparkles className="w-3 h-3 mr-1.5 group-hover:rotate-12 transition-transform" />
                  {choice}
                </motion.button>)}
            </div>
          </motion.div>}
      </AnimatePresence>

      {/* Input Container */}
      <div className="relative">
        <div className="flex items-end space-x-3">
          {/* Attachment Button */}
          <button type="button" className="flex-shrink-0 p-2.5 text-muted-foreground hover:text-foreground hover:bg-accent rounded-xl transition-colors" title="Attach file">
            <Paperclip className="w-5 h-5" />
          </button>

          {/* Text Input */}
          <div className="flex-1 relative">
            <textarea ref={textareaRef} value={value} onChange={e => onChange(e.target.value)} onKeyDown={handleKeyDown} placeholder={placeholder} disabled={isLoading} className={cn("w-full px-4 py-3 bg-card border border-border rounded-2xl resize-none", "focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent", "text-sm leading-relaxed placeholder:text-muted-foreground", "disabled:opacity-50 disabled:cursor-not-allowed", "transition-all duration-200")} rows={1} style={{
            minHeight: '48px',
            maxHeight: '120px'
          }} />
            
            {/* Character count or loading indicator */}
            {(value.length > 0 || isLoading) && <div className="absolute bottom-2 right-3 flex items-center space-x-2">
                {isLoading && <div className="flex space-x-1">
                    <div className="w-1 h-1 bg-muted-foreground rounded-full animate-bounce" style={{
                animationDelay: '0ms'
              }} />
                    <div className="w-1 h-1 bg-muted-foreground rounded-full animate-bounce" style={{
                animationDelay: '150ms'
              }} />
                    <div className="w-1 h-1 bg-muted-foreground rounded-full animate-bounce" style={{
                animationDelay: '300ms'
              }} />
                  </div>}
                {value.length > 100 && <span className={cn("text-xs", value.length > 500 ? "text-destructive" : "text-muted-foreground")}>
                    {value.length}
                  </span>}
              </div>}
          </div>

          {/* Voice Recording Button */}
          <button type="button" onClick={toggleRecording} className={cn("flex-shrink-0 p-2.5 rounded-xl transition-all duration-200", isRecording ? "bg-destructive text-destructive-foreground animate-pulse" : "text-muted-foreground hover:text-foreground hover:bg-accent")} title={isRecording ? "Stop recording" : "Voice message"}>
            {isRecording ? <Square className="w-5 h-5" /> : <Mic className="w-5 h-5" />}
          </button>

          {/* Send Button */}
          <motion.button type="button" onClick={onSend} disabled={!value.trim() || isLoading} whileHover={{
          scale: 1.05
        }} whileTap={{
          scale: 0.95
        }} className={cn("flex-shrink-0 p-3 rounded-2xl transition-all duration-200", "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2", value.trim() && !isLoading ? "bg-primary hover:bg-primary/90 text-primary-foreground shadow-md hover:shadow-lg" : "bg-muted text-muted-foreground cursor-not-allowed")} title="Send message">
            <Send className="w-4 h-4" />
          </motion.button>
        </div>

        {/* Recording Indicator */}
        <AnimatePresence>
          {isRecording && <motion.div initial={{
          opacity: 0,
          y: 10
        }} animate={{
          opacity: 1,
          y: 0
        }} exit={{
          opacity: 0,
          y: -10
        }} className="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-destructive text-destructive-foreground px-3 py-1.5 rounded-full text-xs font-medium flex items-center space-x-2">
              <div className="w-2 h-2 bg-destructive-foreground rounded-full animate-pulse" />
              <span>Recording...</span>
            </motion.div>}
        </AnimatePresence>
      </div>

      {/* Keyboard Shortcut Hint */}
      {value.length > 0 && !isLoading && <motion.p initial={{
      opacity: 0
    }} animate={{
      opacity: 1
    }} className="text-xs text-muted-foreground mt-2 text-center">
          Press <kbd className="px-1.5 py-0.5 bg-muted rounded text-xs">Enter</kbd> to send, 
          <kbd className="px-1.5 py-0.5 bg-muted rounded text-xs ml-1">Shift + Enter</kbd> for new line
        </motion.p>}
    </div>;
};
export default FloatingInputBox;