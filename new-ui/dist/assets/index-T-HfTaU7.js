(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const h of document.querySelectorAll('link[rel="modulepreload"]'))r(h);new MutationObserver(h=>{for(const m of h)if(m.type==="childList")for(const S of m.addedNodes)S.tagName==="LINK"&&S.rel==="modulepreload"&&r(S)}).observe(document,{childList:!0,subtree:!0});function o(h){const m={};return h.integrity&&(m.integrity=h.integrity),h.referrerPolicy&&(m.referrerPolicy=h.referrerPolicy),h.crossOrigin==="use-credentials"?m.credentials="include":h.crossOrigin==="anonymous"?m.credentials="omit":m.credentials="same-origin",m}function r(h){if(h.ep)return;h.ep=!0;const m=o(h);fetch(h.href,m)}})();function x0(i){return i&&i.__esModule&&Object.prototype.hasOwnProperty.call(i,"default")?i.default:i}var Ps={exports:{}},yu={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ih;function S0(){if(ih)return yu;ih=1;var i=Symbol.for("react.transitional.element"),s=Symbol.for("react.fragment");function o(r,h,m){var S=null;if(m!==void 0&&(S=""+m),h.key!==void 0&&(S=""+h.key),"key"in h){m={};for(var M in h)M!=="key"&&(m[M]=h[M])}else m=h;return h=m.ref,{$$typeof:i,type:r,key:S,ref:h!==void 0?h:null,props:m}}return yu.Fragment=s,yu.jsx=o,yu.jsxs=o,yu}var ch;function A0(){return ch||(ch=1,Ps.exports=S0()),Ps.exports}var z=A0(),Is={exports:{}},rt={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sh;function E0(){if(sh)return rt;sh=1;var i=Symbol.for("react.transitional.element"),s=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),h=Symbol.for("react.profiler"),m=Symbol.for("react.consumer"),S=Symbol.for("react.context"),M=Symbol.for("react.forward_ref"),x=Symbol.for("react.suspense"),p=Symbol.for("react.memo"),N=Symbol.for("react.lazy"),j=Symbol.iterator;function O(g){return g===null||typeof g!="object"?null:(g=j&&g[j]||g["@@iterator"],typeof g=="function"?g:null)}var X={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},G=Object.assign,H={};function tt(g,U,k){this.props=g,this.context=U,this.refs=H,this.updater=k||X}tt.prototype.isReactComponent={},tt.prototype.setState=function(g,U){if(typeof g!="object"&&typeof g!="function"&&g!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,g,U,"setState")},tt.prototype.forceUpdate=function(g){this.updater.enqueueForceUpdate(this,g,"forceUpdate")};function ut(){}ut.prototype=tt.prototype;function nt(g,U,k){this.props=g,this.context=U,this.refs=H,this.updater=k||X}var dt=nt.prototype=new ut;dt.constructor=nt,G(dt,tt.prototype),dt.isPureReactComponent=!0;var At=Array.isArray,W={H:null,A:null,T:null,S:null},B=Object.prototype.hasOwnProperty;function at(g,U,k,K,q,ct){return k=ct.ref,{$$typeof:i,type:g,key:U,ref:k!==void 0?k:null,props:ct}}function st(g,U){return at(g.type,U,void 0,void 0,void 0,g.props)}function Y(g){return typeof g=="object"&&g!==null&&g.$$typeof===i}function $(g){var U={"=":"=0",":":"=2"};return"$"+g.replace(/[=:]/g,function(k){return U[k]})}var pt=/\/+/g;function _t(g,U){return typeof g=="object"&&g!==null&&g.key!=null?$(""+g.key):U.toString(36)}function zt(){}function Mt(g){switch(g.status){case"fulfilled":return g.value;case"rejected":throw g.reason;default:switch(typeof g.status=="string"?g.then(zt,zt):(g.status="pending",g.then(function(U){g.status==="pending"&&(g.status="fulfilled",g.value=U)},function(U){g.status==="pending"&&(g.status="rejected",g.reason=U)})),g.status){case"fulfilled":return g.value;case"rejected":throw g.reason}}throw g}function Nt(g,U,k,K,q){var ct=typeof g;(ct==="undefined"||ct==="boolean")&&(g=null);var I=!1;if(g===null)I=!0;else switch(ct){case"bigint":case"string":case"number":I=!0;break;case"object":switch(g.$$typeof){case i:case s:I=!0;break;case N:return I=g._init,Nt(I(g._payload),U,k,K,q)}}if(I)return q=q(g),I=K===""?"."+_t(g,0):K,At(q)?(k="",I!=null&&(k=I.replace(pt,"$&/")+"/"),Nt(q,U,k,"",function(Ut){return Ut})):q!=null&&(Y(q)&&(q=st(q,k+(q.key==null||g&&g.key===q.key?"":(""+q.key).replace(pt,"$&/")+"/")+I)),U.push(q)),1;I=0;var Ct=K===""?".":K+":";if(At(g))for(var bt=0;bt<g.length;bt++)K=g[bt],ct=Ct+_t(K,bt),I+=Nt(K,U,k,ct,q);else if(bt=O(g),typeof bt=="function")for(g=bt.call(g),bt=0;!(K=g.next()).done;)K=K.value,ct=Ct+_t(K,bt++),I+=Nt(K,U,k,ct,q);else if(ct==="object"){if(typeof g.then=="function")return Nt(Mt(g),U,k,K,q);throw U=String(g),Error("Objects are not valid as a React child (found: "+(U==="[object Object]"?"object with keys {"+Object.keys(g).join(", ")+"}":U)+"). If you meant to render a collection of children, use an array instead.")}return I}function _(g,U,k){if(g==null)return g;var K=[],q=0;return Nt(g,K,"","",function(ct){return U.call(k,ct,q++)}),K}function F(g){if(g._status===-1){var U=g._result;U=U(),U.then(function(k){(g._status===0||g._status===-1)&&(g._status=1,g._result=k)},function(k){(g._status===0||g._status===-1)&&(g._status=2,g._result=k)}),g._status===-1&&(g._status=0,g._result=U)}if(g._status===1)return g._result.default;throw g._result}var L=typeof reportError=="function"?reportError:function(g){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var U=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof g=="object"&&g!==null&&typeof g.message=="string"?String(g.message):String(g),error:g});if(!window.dispatchEvent(U))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",g);return}console.error(g)};function lt(){}return rt.Children={map:_,forEach:function(g,U,k){_(g,function(){U.apply(this,arguments)},k)},count:function(g){var U=0;return _(g,function(){U++}),U},toArray:function(g){return _(g,function(U){return U})||[]},only:function(g){if(!Y(g))throw Error("React.Children.only expected to receive a single React element child.");return g}},rt.Component=tt,rt.Fragment=o,rt.Profiler=h,rt.PureComponent=nt,rt.StrictMode=r,rt.Suspense=x,rt.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=W,rt.act=function(){throw Error("act(...) is not supported in production builds of React.")},rt.cache=function(g){return function(){return g.apply(null,arguments)}},rt.cloneElement=function(g,U,k){if(g==null)throw Error("The argument must be a React element, but you passed "+g+".");var K=G({},g.props),q=g.key,ct=void 0;if(U!=null)for(I in U.ref!==void 0&&(ct=void 0),U.key!==void 0&&(q=""+U.key),U)!B.call(U,I)||I==="key"||I==="__self"||I==="__source"||I==="ref"&&U.ref===void 0||(K[I]=U[I]);var I=arguments.length-2;if(I===1)K.children=k;else if(1<I){for(var Ct=Array(I),bt=0;bt<I;bt++)Ct[bt]=arguments[bt+2];K.children=Ct}return at(g.type,q,void 0,void 0,ct,K)},rt.createContext=function(g){return g={$$typeof:S,_currentValue:g,_currentValue2:g,_threadCount:0,Provider:null,Consumer:null},g.Provider=g,g.Consumer={$$typeof:m,_context:g},g},rt.createElement=function(g,U,k){var K,q={},ct=null;if(U!=null)for(K in U.key!==void 0&&(ct=""+U.key),U)B.call(U,K)&&K!=="key"&&K!=="__self"&&K!=="__source"&&(q[K]=U[K]);var I=arguments.length-2;if(I===1)q.children=k;else if(1<I){for(var Ct=Array(I),bt=0;bt<I;bt++)Ct[bt]=arguments[bt+2];q.children=Ct}if(g&&g.defaultProps)for(K in I=g.defaultProps,I)q[K]===void 0&&(q[K]=I[K]);return at(g,ct,void 0,void 0,null,q)},rt.createRef=function(){return{current:null}},rt.forwardRef=function(g){return{$$typeof:M,render:g}},rt.isValidElement=Y,rt.lazy=function(g){return{$$typeof:N,_payload:{_status:-1,_result:g},_init:F}},rt.memo=function(g,U){return{$$typeof:p,type:g,compare:U===void 0?null:U}},rt.startTransition=function(g){var U=W.T,k={};W.T=k;try{var K=g(),q=W.S;q!==null&&q(k,K),typeof K=="object"&&K!==null&&typeof K.then=="function"&&K.then(lt,L)}catch(ct){L(ct)}finally{W.T=U}},rt.unstable_useCacheRefresh=function(){return W.H.useCacheRefresh()},rt.use=function(g){return W.H.use(g)},rt.useActionState=function(g,U,k){return W.H.useActionState(g,U,k)},rt.useCallback=function(g,U){return W.H.useCallback(g,U)},rt.useContext=function(g){return W.H.useContext(g)},rt.useDebugValue=function(){},rt.useDeferredValue=function(g,U){return W.H.useDeferredValue(g,U)},rt.useEffect=function(g,U){return W.H.useEffect(g,U)},rt.useId=function(){return W.H.useId()},rt.useImperativeHandle=function(g,U,k){return W.H.useImperativeHandle(g,U,k)},rt.useInsertionEffect=function(g,U){return W.H.useInsertionEffect(g,U)},rt.useLayoutEffect=function(g,U){return W.H.useLayoutEffect(g,U)},rt.useMemo=function(g,U){return W.H.useMemo(g,U)},rt.useOptimistic=function(g,U){return W.H.useOptimistic(g,U)},rt.useReducer=function(g,U,k){return W.H.useReducer(g,U,k)},rt.useRef=function(g){return W.H.useRef(g)},rt.useState=function(g){return W.H.useState(g)},rt.useSyncExternalStore=function(g,U,k){return W.H.useSyncExternalStore(g,U,k)},rt.useTransition=function(){return W.H.useTransition()},rt.version="19.0.0",rt}var rh;function xr(){return rh||(rh=1,Is.exports=E0()),Is.exports}var w=xr();const $e=x0(w);var tr={exports:{}},bu={},er={exports:{}},ar={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var oh;function M0(){return oh||(oh=1,function(i){function s(_,F){var L=_.length;_.push(F);t:for(;0<L;){var lt=L-1>>>1,g=_[lt];if(0<h(g,F))_[lt]=F,_[L]=g,L=lt;else break t}}function o(_){return _.length===0?null:_[0]}function r(_){if(_.length===0)return null;var F=_[0],L=_.pop();if(L!==F){_[0]=L;t:for(var lt=0,g=_.length,U=g>>>1;lt<U;){var k=2*(lt+1)-1,K=_[k],q=k+1,ct=_[q];if(0>h(K,L))q<g&&0>h(ct,K)?(_[lt]=ct,_[q]=L,lt=q):(_[lt]=K,_[k]=L,lt=k);else if(q<g&&0>h(ct,L))_[lt]=ct,_[q]=L,lt=q;else break t}}return F}function h(_,F){var L=_.sortIndex-F.sortIndex;return L!==0?L:_.id-F.id}if(i.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var m=performance;i.unstable_now=function(){return m.now()}}else{var S=Date,M=S.now();i.unstable_now=function(){return S.now()-M}}var x=[],p=[],N=1,j=null,O=3,X=!1,G=!1,H=!1,tt=typeof setTimeout=="function"?setTimeout:null,ut=typeof clearTimeout=="function"?clearTimeout:null,nt=typeof setImmediate<"u"?setImmediate:null;function dt(_){for(var F=o(p);F!==null;){if(F.callback===null)r(p);else if(F.startTime<=_)r(p),F.sortIndex=F.expirationTime,s(x,F);else break;F=o(p)}}function At(_){if(H=!1,dt(_),!G)if(o(x)!==null)G=!0,Mt();else{var F=o(p);F!==null&&Nt(At,F.startTime-_)}}var W=!1,B=-1,at=5,st=-1;function Y(){return!(i.unstable_now()-st<at)}function $(){if(W){var _=i.unstable_now();st=_;var F=!0;try{t:{G=!1,H&&(H=!1,ut(B),B=-1),X=!0;var L=O;try{e:{for(dt(_),j=o(x);j!==null&&!(j.expirationTime>_&&Y());){var lt=j.callback;if(typeof lt=="function"){j.callback=null,O=j.priorityLevel;var g=lt(j.expirationTime<=_);if(_=i.unstable_now(),typeof g=="function"){j.callback=g,dt(_),F=!0;break e}j===o(x)&&r(x),dt(_)}else r(x);j=o(x)}if(j!==null)F=!0;else{var U=o(p);U!==null&&Nt(At,U.startTime-_),F=!1}}break t}finally{j=null,O=L,X=!1}F=void 0}}finally{F?pt():W=!1}}}var pt;if(typeof nt=="function")pt=function(){nt($)};else if(typeof MessageChannel<"u"){var _t=new MessageChannel,zt=_t.port2;_t.port1.onmessage=$,pt=function(){zt.postMessage(null)}}else pt=function(){tt($,0)};function Mt(){W||(W=!0,pt())}function Nt(_,F){B=tt(function(){_(i.unstable_now())},F)}i.unstable_IdlePriority=5,i.unstable_ImmediatePriority=1,i.unstable_LowPriority=4,i.unstable_NormalPriority=3,i.unstable_Profiling=null,i.unstable_UserBlockingPriority=2,i.unstable_cancelCallback=function(_){_.callback=null},i.unstable_continueExecution=function(){G||X||(G=!0,Mt())},i.unstable_forceFrameRate=function(_){0>_||125<_?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):at=0<_?Math.floor(1e3/_):5},i.unstable_getCurrentPriorityLevel=function(){return O},i.unstable_getFirstCallbackNode=function(){return o(x)},i.unstable_next=function(_){switch(O){case 1:case 2:case 3:var F=3;break;default:F=O}var L=O;O=F;try{return _()}finally{O=L}},i.unstable_pauseExecution=function(){},i.unstable_requestPaint=function(){},i.unstable_runWithPriority=function(_,F){switch(_){case 1:case 2:case 3:case 4:case 5:break;default:_=3}var L=O;O=_;try{return F()}finally{O=L}},i.unstable_scheduleCallback=function(_,F,L){var lt=i.unstable_now();switch(typeof L=="object"&&L!==null?(L=L.delay,L=typeof L=="number"&&0<L?lt+L:lt):L=lt,_){case 1:var g=-1;break;case 2:g=250;break;case 5:g=1073741823;break;case 4:g=1e4;break;default:g=5e3}return g=L+g,_={id:N++,callback:F,priorityLevel:_,startTime:L,expirationTime:g,sortIndex:-1},L>lt?(_.sortIndex=L,s(p,_),o(x)===null&&_===o(p)&&(H?(ut(B),B=-1):H=!0,Nt(At,L-lt))):(_.sortIndex=g,s(x,_),G||X||(G=!0,Mt())),_},i.unstable_shouldYield=Y,i.unstable_wrapCallback=function(_){var F=O;return function(){var L=O;O=F;try{return _.apply(this,arguments)}finally{O=L}}}}(ar)),ar}var fh;function T0(){return fh||(fh=1,er.exports=M0()),er.exports}var lr={exports:{}},re={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var dh;function D0(){if(dh)return re;dh=1;var i=xr();function s(x){var p="https://react.dev/errors/"+x;if(1<arguments.length){p+="?args[]="+encodeURIComponent(arguments[1]);for(var N=2;N<arguments.length;N++)p+="&args[]="+encodeURIComponent(arguments[N])}return"Minified React error #"+x+"; visit "+p+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(){}var r={d:{f:o,r:function(){throw Error(s(522))},D:o,C:o,L:o,m:o,X:o,S:o,M:o},p:0,findDOMNode:null},h=Symbol.for("react.portal");function m(x,p,N){var j=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:h,key:j==null?null:""+j,children:x,containerInfo:p,implementation:N}}var S=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function M(x,p){if(x==="font")return"";if(typeof p=="string")return p==="use-credentials"?p:""}return re.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,re.createPortal=function(x,p){var N=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!p||p.nodeType!==1&&p.nodeType!==9&&p.nodeType!==11)throw Error(s(299));return m(x,p,null,N)},re.flushSync=function(x){var p=S.T,N=r.p;try{if(S.T=null,r.p=2,x)return x()}finally{S.T=p,r.p=N,r.d.f()}},re.preconnect=function(x,p){typeof x=="string"&&(p?(p=p.crossOrigin,p=typeof p=="string"?p==="use-credentials"?p:"":void 0):p=null,r.d.C(x,p))},re.prefetchDNS=function(x){typeof x=="string"&&r.d.D(x)},re.preinit=function(x,p){if(typeof x=="string"&&p&&typeof p.as=="string"){var N=p.as,j=M(N,p.crossOrigin),O=typeof p.integrity=="string"?p.integrity:void 0,X=typeof p.fetchPriority=="string"?p.fetchPriority:void 0;N==="style"?r.d.S(x,typeof p.precedence=="string"?p.precedence:void 0,{crossOrigin:j,integrity:O,fetchPriority:X}):N==="script"&&r.d.X(x,{crossOrigin:j,integrity:O,fetchPriority:X,nonce:typeof p.nonce=="string"?p.nonce:void 0})}},re.preinitModule=function(x,p){if(typeof x=="string")if(typeof p=="object"&&p!==null){if(p.as==null||p.as==="script"){var N=M(p.as,p.crossOrigin);r.d.M(x,{crossOrigin:N,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0})}}else p==null&&r.d.M(x)},re.preload=function(x,p){if(typeof x=="string"&&typeof p=="object"&&p!==null&&typeof p.as=="string"){var N=p.as,j=M(N,p.crossOrigin);r.d.L(x,N,{crossOrigin:j,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0,type:typeof p.type=="string"?p.type:void 0,fetchPriority:typeof p.fetchPriority=="string"?p.fetchPriority:void 0,referrerPolicy:typeof p.referrerPolicy=="string"?p.referrerPolicy:void 0,imageSrcSet:typeof p.imageSrcSet=="string"?p.imageSrcSet:void 0,imageSizes:typeof p.imageSizes=="string"?p.imageSizes:void 0,media:typeof p.media=="string"?p.media:void 0})}},re.preloadModule=function(x,p){if(typeof x=="string")if(p){var N=M(p.as,p.crossOrigin);r.d.m(x,{as:typeof p.as=="string"&&p.as!=="script"?p.as:void 0,crossOrigin:N,integrity:typeof p.integrity=="string"?p.integrity:void 0})}else r.d.m(x)},re.requestFormReset=function(x){r.d.r(x)},re.unstable_batchedUpdates=function(x,p){return x(p)},re.useFormState=function(x,p,N){return S.H.useFormState(x,p,N)},re.useFormStatus=function(){return S.H.useHostTransitionStatus()},re.version="19.0.0",re}var hh;function jh(){if(hh)return lr.exports;hh=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(s){console.error(s)}}return i(),lr.exports=D0(),lr.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var gh;function z0(){if(gh)return bu;gh=1;var i=T0(),s=xr(),o=jh();function r(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)e+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function h(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}var m=Symbol.for("react.element"),S=Symbol.for("react.transitional.element"),M=Symbol.for("react.portal"),x=Symbol.for("react.fragment"),p=Symbol.for("react.strict_mode"),N=Symbol.for("react.profiler"),j=Symbol.for("react.provider"),O=Symbol.for("react.consumer"),X=Symbol.for("react.context"),G=Symbol.for("react.forward_ref"),H=Symbol.for("react.suspense"),tt=Symbol.for("react.suspense_list"),ut=Symbol.for("react.memo"),nt=Symbol.for("react.lazy"),dt=Symbol.for("react.offscreen"),At=Symbol.for("react.memo_cache_sentinel"),W=Symbol.iterator;function B(t){return t===null||typeof t!="object"?null:(t=W&&t[W]||t["@@iterator"],typeof t=="function"?t:null)}var at=Symbol.for("react.client.reference");function st(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===at?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case x:return"Fragment";case M:return"Portal";case N:return"Profiler";case p:return"StrictMode";case H:return"Suspense";case tt:return"SuspenseList"}if(typeof t=="object")switch(t.$$typeof){case X:return(t.displayName||"Context")+".Provider";case O:return(t._context.displayName||"Context")+".Consumer";case G:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case ut:return e=t.displayName||null,e!==null?e:st(t.type)||"Memo";case nt:e=t._payload,t=t._init;try{return st(t(e))}catch{}}return null}var Y=s.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,$=Object.assign,pt,_t;function zt(t){if(pt===void 0)try{throw Error()}catch(a){var e=a.stack.trim().match(/\n( *(at )?)/);pt=e&&e[1]||"",_t=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+pt+t+_t}var Mt=!1;function Nt(t,e){if(!t||Mt)return"";Mt=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(e){var C=function(){throw Error()};if(Object.defineProperty(C.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(C,[])}catch(T){var E=T}Reflect.construct(t,[],C)}else{try{C.call()}catch(T){E=T}t.call(C.prototype)}}else{try{throw Error()}catch(T){E=T}(C=t())&&typeof C.catch=="function"&&C.catch(function(){})}}catch(T){if(T&&E&&typeof T.stack=="string")return[T.stack,E.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=l.DetermineComponentFrameRoot(),c=u[0],f=u[1];if(c&&f){var d=c.split(`
`),y=f.split(`
`);for(n=l=0;l<d.length&&!d[l].includes("DetermineComponentFrameRoot");)l++;for(;n<y.length&&!y[n].includes("DetermineComponentFrameRoot");)n++;if(l===d.length||n===y.length)for(l=d.length-1,n=y.length-1;1<=l&&0<=n&&d[l]!==y[n];)n--;for(;1<=l&&0<=n;l--,n--)if(d[l]!==y[n]){if(l!==1||n!==1)do if(l--,n--,0>n||d[l]!==y[n]){var D=`
`+d[l].replace(" at new "," at ");return t.displayName&&D.includes("<anonymous>")&&(D=D.replace("<anonymous>",t.displayName)),D}while(1<=l&&0<=n);break}}}finally{Mt=!1,Error.prepareStackTrace=a}return(a=t?t.displayName||t.name:"")?zt(a):""}function _(t){switch(t.tag){case 26:case 27:case 5:return zt(t.type);case 16:return zt("Lazy");case 13:return zt("Suspense");case 19:return zt("SuspenseList");case 0:case 15:return t=Nt(t.type,!1),t;case 11:return t=Nt(t.type.render,!1),t;case 1:return t=Nt(t.type,!0),t;default:return""}}function F(t){try{var e="";do e+=_(t),t=t.return;while(t);return e}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function L(t){var e=t,a=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(a=e.return),t=e.return;while(t)}return e.tag===3?a:null}function lt(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function g(t){if(L(t)!==t)throw Error(r(188))}function U(t){var e=t.alternate;if(!e){if(e=L(t),e===null)throw Error(r(188));return e!==t?null:t}for(var a=t,l=e;;){var n=a.return;if(n===null)break;var u=n.alternate;if(u===null){if(l=n.return,l!==null){a=l;continue}break}if(n.child===u.child){for(u=n.child;u;){if(u===a)return g(n),t;if(u===l)return g(n),e;u=u.sibling}throw Error(r(188))}if(a.return!==l.return)a=n,l=u;else{for(var c=!1,f=n.child;f;){if(f===a){c=!0,a=n,l=u;break}if(f===l){c=!0,l=n,a=u;break}f=f.sibling}if(!c){for(f=u.child;f;){if(f===a){c=!0,a=u,l=n;break}if(f===l){c=!0,l=u,a=n;break}f=f.sibling}if(!c)throw Error(r(189))}}if(a.alternate!==l)throw Error(r(190))}if(a.tag!==3)throw Error(r(188));return a.stateNode.current===a?t:e}function k(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=k(t),e!==null)return e;t=t.sibling}return null}var K=Array.isArray,q=o.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,ct={pending:!1,data:null,method:null,action:null},I=[],Ct=-1;function bt(t){return{current:t}}function Ut(t){0>Ct||(t.current=I[Ct],I[Ct]=null,Ct--)}function gt(t,e){Ct++,I[Ct]=t.current,t.current=e}var me=bt(null),Ee=bt(null),We=bt(null),Ce=bt(null);function ha(t,e){switch(gt(We,e),gt(Ee,t),gt(me,null),t=e.nodeType,t){case 9:case 11:e=(e=e.documentElement)&&(e=e.namespaceURI)?jd(e):0;break;default:if(t=t===8?e.parentNode:e,e=t.tagName,t=t.namespaceURI)t=jd(t),e=Hd(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}Ut(me),gt(me,e)}function Me(){Ut(me),Ut(Ee),Ut(We)}function _l(t){t.memoizedState!==null&&gt(Ce,t);var e=me.current,a=Hd(e,t.type);e!==a&&(gt(Ee,t),gt(me,a))}function nl(t){Ee.current===t&&(Ut(me),Ut(Ee)),Ce.current===t&&(Ut(Ce),hu._currentValue=ct)}var ul=Object.prototype.hasOwnProperty,Ue=i.unstable_scheduleCallback,_a=i.unstable_cancelCallback,Cl=i.unstable_shouldYield,wu=i.unstable_requestPaint,Te=i.unstable_now,Zi=i.unstable_getCurrentPriorityLevel,Ru=i.unstable_ImmediatePriority,ga=i.unstable_UserBlockingPriority,Fe=i.unstable_NormalPriority,il=i.unstable_LowPriority,En=i.unstable_IdlePriority,na=i.log,Ou=i.unstable_setDisableYieldValue,cl=null,fe=null;function Mn(t){if(fe&&typeof fe.onCommitFiberRoot=="function")try{fe.onCommitFiberRoot(cl,t,void 0,(t.current.flags&128)===128)}catch{}}function Pe(t){if(typeof na=="function"&&Ou(t),fe&&typeof fe.setStrictMode=="function")try{fe.setStrictMode(cl,t)}catch{}}var de=Math.clz32?Math.clz32:ki,Nu=Math.log,Ki=Math.LN2;function ki(t){return t>>>=0,t===0?32:31-(Nu(t)/Ki|0)|0}var Ul=128,Lt=4194304;function Xt(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194176;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Ft(t,e){var a=t.pendingLanes;if(a===0)return 0;var l=0,n=t.suspendedLanes,u=t.pingedLanes,c=t.warmLanes;t=t.finishedLanes!==0;var f=a&134217727;return f!==0?(a=f&~n,a!==0?l=Xt(a):(u&=f,u!==0?l=Xt(u):t||(c=f&~c,c!==0&&(l=Xt(c))))):(f=a&~n,f!==0?l=Xt(f):u!==0?l=Xt(u):t||(c=a&~c,c!==0&&(l=Xt(c)))),l===0?0:e!==0&&e!==l&&(e&n)===0&&(n=l&-l,c=e&-e,n>=c||n===32&&(c&4194176)!==0)?e:l}function ve(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function pe(t,e){switch(t){case 1:case 2:case 4:case 8:return e+250;case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ce(){var t=Ul;return Ul<<=1,(Ul&4194176)===0&&(Ul=128),t}function De(){var t=Lt;return Lt<<=1,(Lt&62914560)===0&&(Lt=4194304),t}function ua(t){for(var e=[],a=0;31>a;a++)e.push(t);return e}function jt(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function ia(t,e,a,l,n,u){var c=t.pendingLanes;t.pendingLanes=a,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=a,t.entangledLanes&=a,t.errorRecoveryDisabledLanes&=a,t.shellSuspendCounter=0;var f=t.entanglements,d=t.expirationTimes,y=t.hiddenUpdates;for(a=c&~a;0<a;){var D=31-de(a),C=1<<D;f[D]=0,d[D]=-1;var E=y[D];if(E!==null)for(y[D]=null,D=0;D<E.length;D++){var T=E[D];T!==null&&(T.lane&=-536870913)}a&=~C}l!==0&&ze(t,l,0),u!==0&&n===0&&t.tag!==0&&(t.suspendedLanes|=u&~(c&~e))}function ze(t,e,a){t.pendingLanes|=e,t.suspendedLanes&=~e;var l=31-de(e);t.entangledLanes|=e,t.entanglements[l]=t.entanglements[l]|1073741824|a&4194218}function je(t,e){var a=t.entangledLanes|=e;for(t=t.entanglements;a;){var l=31-de(a),n=1<<l;n&e|t[l]&e&&(t[l]|=e),a&=~n}}function Ca(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function Ua(){var t=q.p;return t!==0?t:(t=window.event,t===void 0?32:th(t.type))}function ca(t,e){var a=q.p;try{return q.p=t,e()}finally{q.p=a}}var ye=Math.random().toString(36).slice(2),Vt="__reactFiber$"+ye,be="__reactProps$"+ye,jl="__reactContainer$"+ye,Ji="__reactEvents$"+ye,fg="__reactListeners$"+ye,dg="__reactHandles$"+ye,Nr="__reactResources$"+ye,Tn="__reactMarker$"+ye;function $i(t){delete t[Vt],delete t[be],delete t[Ji],delete t[fg],delete t[dg]}function sl(t){var e=t[Vt];if(e)return e;for(var a=t.parentNode;a;){if(e=a[jl]||a[Vt]){if(a=e.alternate,e.child!==null||a!==null&&a.child!==null)for(t=Yd(t);t!==null;){if(a=t[Vt])return a;t=Yd(t)}return e}t=a,a=t.parentNode}return null}function Hl(t){if(t=t[Vt]||t[jl]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function Dn(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(r(33))}function Bl(t){var e=t[Nr];return e||(e=t[Nr]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Pt(t){t[Tn]=!0}var _r=new Set,Cr={};function rl(t,e){ql(t,e),ql(t+"Capture",e)}function ql(t,e){for(Cr[t]=e,t=0;t<e.length;t++)_r.add(e[t])}var ma=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),hg=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ur={},jr={};function gg(t){return ul.call(jr,t)?!0:ul.call(Ur,t)?!1:hg.test(t)?jr[t]=!0:(Ur[t]=!0,!1)}function _u(t,e,a){if(gg(e))if(a===null)t.removeAttribute(e);else{switch(typeof a){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var l=e.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+a)}}function Cu(t,e,a){if(a===null)t.removeAttribute(e);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+a)}}function va(t,e,a,l){if(l===null)t.removeAttribute(a);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(a);return}t.setAttributeNS(e,a,""+l)}}function He(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Hr(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function mg(t){var e=Hr(t)?"checked":"value",a=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),l=""+t[e];if(!t.hasOwnProperty(e)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var n=a.get,u=a.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return n.call(this)},set:function(c){l=""+c,u.call(this,c)}}),Object.defineProperty(t,e,{enumerable:a.enumerable}),{getValue:function(){return l},setValue:function(c){l=""+c},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function Uu(t){t._valueTracker||(t._valueTracker=mg(t))}function Br(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var a=e.getValue(),l="";return t&&(l=Hr(t)?t.checked?"true":"false":t.value),t=l,t!==a?(e.setValue(t),!0):!1}function ju(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var vg=/[\n"\\]/g;function Be(t){return t.replace(vg,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function Wi(t,e,a,l,n,u,c,f){t.name="",c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"?t.type=c:t.removeAttribute("type"),e!=null?c==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+He(e)):t.value!==""+He(e)&&(t.value=""+He(e)):c!=="submit"&&c!=="reset"||t.removeAttribute("value"),e!=null?Fi(t,c,He(e)):a!=null?Fi(t,c,He(a)):l!=null&&t.removeAttribute("value"),n==null&&u!=null&&(t.defaultChecked=!!u),n!=null&&(t.checked=n&&typeof n!="function"&&typeof n!="symbol"),f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"?t.name=""+He(f):t.removeAttribute("name")}function qr(t,e,a,l,n,u,c,f){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(t.type=u),e!=null||a!=null){if(!(u!=="submit"&&u!=="reset"||e!=null))return;a=a!=null?""+He(a):"",e=e!=null?""+He(e):a,f||e===t.value||(t.value=e),t.defaultValue=e}l=l??n,l=typeof l!="function"&&typeof l!="symbol"&&!!l,t.checked=f?t.checked:!!l,t.defaultChecked=!!l,c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(t.name=c)}function Fi(t,e,a){e==="number"&&ju(t.ownerDocument)===t||t.defaultValue===""+a||(t.defaultValue=""+a)}function Yl(t,e,a,l){if(t=t.options,e){e={};for(var n=0;n<a.length;n++)e["$"+a[n]]=!0;for(a=0;a<t.length;a++)n=e.hasOwnProperty("$"+t[a].value),t[a].selected!==n&&(t[a].selected=n),n&&l&&(t[a].defaultSelected=!0)}else{for(a=""+He(a),e=null,n=0;n<t.length;n++){if(t[n].value===a){t[n].selected=!0,l&&(t[n].defaultSelected=!0);return}e!==null||t[n].disabled||(e=t[n])}e!==null&&(e.selected=!0)}}function Yr(t,e,a){if(e!=null&&(e=""+He(e),e!==t.value&&(t.value=e),a==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=a!=null?""+He(a):""}function Gr(t,e,a,l){if(e==null){if(l!=null){if(a!=null)throw Error(r(92));if(K(l)){if(1<l.length)throw Error(r(93));l=l[0]}a=l}a==null&&(a=""),e=a}a=He(e),t.defaultValue=a,l=t.textContent,l===a&&l!==""&&l!==null&&(t.value=l)}function Gl(t,e){if(e){var a=t.firstChild;if(a&&a===t.lastChild&&a.nodeType===3){a.nodeValue=e;return}}t.textContent=e}var pg=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Lr(t,e,a){var l=e.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?l?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":l?t.setProperty(e,a):typeof a!="number"||a===0||pg.has(e)?e==="float"?t.cssFloat=a:t[e]=(""+a).trim():t[e]=a+"px"}function Xr(t,e,a){if(e!=null&&typeof e!="object")throw Error(r(62));if(t=t.style,a!=null){for(var l in a)!a.hasOwnProperty(l)||e!=null&&e.hasOwnProperty(l)||(l.indexOf("--")===0?t.setProperty(l,""):l==="float"?t.cssFloat="":t[l]="");for(var n in e)l=e[n],e.hasOwnProperty(n)&&a[n]!==l&&Lr(t,n,l)}else for(var u in e)e.hasOwnProperty(u)&&Lr(t,u,e[u])}function Pi(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var yg=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),bg=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Hu(t){return bg.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var Ii=null;function tc(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var Ll=null,Xl=null;function Qr(t){var e=Hl(t);if(e&&(t=e.stateNode)){var a=t[be]||null;t:switch(t=e.stateNode,e.type){case"input":if(Wi(t,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),e=a.name,a.type==="radio"&&e!=null){for(a=t;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+Be(""+e)+'"][type="radio"]'),e=0;e<a.length;e++){var l=a[e];if(l!==t&&l.form===t.form){var n=l[be]||null;if(!n)throw Error(r(90));Wi(l,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(e=0;e<a.length;e++)l=a[e],l.form===t.form&&Br(l)}break t;case"textarea":Yr(t,a.value,a.defaultValue);break t;case"select":e=a.value,e!=null&&Yl(t,!!a.multiple,e,!1)}}}var ec=!1;function Vr(t,e,a){if(ec)return t(e,a);ec=!0;try{var l=t(e);return l}finally{if(ec=!1,(Ll!==null||Xl!==null)&&(bi(),Ll&&(e=Ll,t=Xl,Xl=Ll=null,Qr(e),t)))for(e=0;e<t.length;e++)Qr(t[e])}}function zn(t,e){var a=t.stateNode;if(a===null)return null;var l=a[be]||null;if(l===null)return null;a=l[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(t=t.type,l=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!l;break t;default:t=!1}if(t)return null;if(a&&typeof a!="function")throw Error(r(231,e,typeof a));return a}var ac=!1;if(ma)try{var wn={};Object.defineProperty(wn,"passive",{get:function(){ac=!0}}),window.addEventListener("test",wn,wn),window.removeEventListener("test",wn,wn)}catch{ac=!1}var ja=null,lc=null,Bu=null;function Zr(){if(Bu)return Bu;var t,e=lc,a=e.length,l,n="value"in ja?ja.value:ja.textContent,u=n.length;for(t=0;t<a&&e[t]===n[t];t++);var c=a-t;for(l=1;l<=c&&e[a-l]===n[u-l];l++);return Bu=n.slice(t,1<l?1-l:void 0)}function qu(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Yu(){return!0}function Kr(){return!1}function xe(t){function e(a,l,n,u,c){this._reactName=a,this._targetInst=n,this.type=l,this.nativeEvent=u,this.target=c,this.currentTarget=null;for(var f in t)t.hasOwnProperty(f)&&(a=t[f],this[f]=a?a(u):u[f]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?Yu:Kr,this.isPropagationStopped=Kr,this}return $(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=Yu)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=Yu)},persist:function(){},isPersistent:Yu}),e}var ol={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Gu=xe(ol),Rn=$({},ol,{view:0,detail:0}),xg=xe(Rn),nc,uc,On,Lu=$({},Rn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:cc,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==On&&(On&&t.type==="mousemove"?(nc=t.screenX-On.screenX,uc=t.screenY-On.screenY):uc=nc=0,On=t),nc)},movementY:function(t){return"movementY"in t?t.movementY:uc}}),kr=xe(Lu),Sg=$({},Lu,{dataTransfer:0}),Ag=xe(Sg),Eg=$({},Rn,{relatedTarget:0}),ic=xe(Eg),Mg=$({},ol,{animationName:0,elapsedTime:0,pseudoElement:0}),Tg=xe(Mg),Dg=$({},ol,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),zg=xe(Dg),wg=$({},ol,{data:0}),Jr=xe(wg),Rg={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Og={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Ng={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function _g(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=Ng[t])?!!e[t]:!1}function cc(){return _g}var Cg=$({},Rn,{key:function(t){if(t.key){var e=Rg[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=qu(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?Og[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:cc,charCode:function(t){return t.type==="keypress"?qu(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?qu(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Ug=xe(Cg),jg=$({},Lu,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),$r=xe(jg),Hg=$({},Rn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:cc}),Bg=xe(Hg),qg=$({},ol,{propertyName:0,elapsedTime:0,pseudoElement:0}),Yg=xe(qg),Gg=$({},Lu,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),Lg=xe(Gg),Xg=$({},ol,{newState:0,oldState:0}),Qg=xe(Xg),Vg=[9,13,27,32],sc=ma&&"CompositionEvent"in window,Nn=null;ma&&"documentMode"in document&&(Nn=document.documentMode);var Zg=ma&&"TextEvent"in window&&!Nn,Wr=ma&&(!sc||Nn&&8<Nn&&11>=Nn),Fr=" ",Pr=!1;function Ir(t,e){switch(t){case"keyup":return Vg.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function to(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Ql=!1;function Kg(t,e){switch(t){case"compositionend":return to(e);case"keypress":return e.which!==32?null:(Pr=!0,Fr);case"textInput":return t=e.data,t===Fr&&Pr?null:t;default:return null}}function kg(t,e){if(Ql)return t==="compositionend"||!sc&&Ir(t,e)?(t=Zr(),Bu=lc=ja=null,Ql=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Wr&&e.locale!=="ko"?null:e.data;default:return null}}var Jg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function eo(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!Jg[t.type]:e==="textarea"}function ao(t,e,a,l){Ll?Xl?Xl.push(l):Xl=[l]:Ll=l,e=Mi(e,"onChange"),0<e.length&&(a=new Gu("onChange","change",null,a,l),t.push({event:a,listeners:e}))}var _n=null,Cn=null;function $g(t){Od(t,0)}function Xu(t){var e=Dn(t);if(Br(e))return t}function lo(t,e){if(t==="change")return e}var no=!1;if(ma){var rc;if(ma){var oc="oninput"in document;if(!oc){var uo=document.createElement("div");uo.setAttribute("oninput","return;"),oc=typeof uo.oninput=="function"}rc=oc}else rc=!1;no=rc&&(!document.documentMode||9<document.documentMode)}function io(){_n&&(_n.detachEvent("onpropertychange",co),Cn=_n=null)}function co(t){if(t.propertyName==="value"&&Xu(Cn)){var e=[];ao(e,Cn,t,tc(t)),Vr($g,e)}}function Wg(t,e,a){t==="focusin"?(io(),_n=e,Cn=a,_n.attachEvent("onpropertychange",co)):t==="focusout"&&io()}function Fg(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Xu(Cn)}function Pg(t,e){if(t==="click")return Xu(e)}function Ig(t,e){if(t==="input"||t==="change")return Xu(e)}function tm(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var we=typeof Object.is=="function"?Object.is:tm;function Un(t,e){if(we(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var a=Object.keys(t),l=Object.keys(e);if(a.length!==l.length)return!1;for(l=0;l<a.length;l++){var n=a[l];if(!ul.call(e,n)||!we(t[n],e[n]))return!1}return!0}function so(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function ro(t,e){var a=so(t);t=0;for(var l;a;){if(a.nodeType===3){if(l=t+a.textContent.length,t<=e&&l>=e)return{node:a,offset:e-t};t=l}t:{for(;a;){if(a.nextSibling){a=a.nextSibling;break t}a=a.parentNode}a=void 0}a=so(a)}}function oo(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?oo(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function fo(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=ju(t.document);e instanceof t.HTMLIFrameElement;){try{var a=typeof e.contentWindow.location.href=="string"}catch{a=!1}if(a)t=e.contentWindow;else break;e=ju(t.document)}return e}function fc(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}function em(t,e){var a=fo(e);e=t.focusedElem;var l=t.selectionRange;if(a!==e&&e&&e.ownerDocument&&oo(e.ownerDocument.documentElement,e)){if(l!==null&&fc(e)){if(t=l.start,a=l.end,a===void 0&&(a=t),"selectionStart"in e)e.selectionStart=t,e.selectionEnd=Math.min(a,e.value.length);else if(a=(t=e.ownerDocument||document)&&t.defaultView||window,a.getSelection){a=a.getSelection();var n=e.textContent.length,u=Math.min(l.start,n);l=l.end===void 0?u:Math.min(l.end,n),!a.extend&&u>l&&(n=l,l=u,u=n),n=ro(e,u);var c=ro(e,l);n&&c&&(a.rangeCount!==1||a.anchorNode!==n.node||a.anchorOffset!==n.offset||a.focusNode!==c.node||a.focusOffset!==c.offset)&&(t=t.createRange(),t.setStart(n.node,n.offset),a.removeAllRanges(),u>l?(a.addRange(t),a.extend(c.node,c.offset)):(t.setEnd(c.node,c.offset),a.addRange(t)))}}for(t=[],a=e;a=a.parentNode;)a.nodeType===1&&t.push({element:a,left:a.scrollLeft,top:a.scrollTop});for(typeof e.focus=="function"&&e.focus(),e=0;e<t.length;e++)a=t[e],a.element.scrollLeft=a.left,a.element.scrollTop=a.top}}var am=ma&&"documentMode"in document&&11>=document.documentMode,Vl=null,dc=null,jn=null,hc=!1;function ho(t,e,a){var l=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;hc||Vl==null||Vl!==ju(l)||(l=Vl,"selectionStart"in l&&fc(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),jn&&Un(jn,l)||(jn=l,l=Mi(dc,"onSelect"),0<l.length&&(e=new Gu("onSelect","select",null,e,a),t.push({event:e,listeners:l}),e.target=Vl)))}function fl(t,e){var a={};return a[t.toLowerCase()]=e.toLowerCase(),a["Webkit"+t]="webkit"+e,a["Moz"+t]="moz"+e,a}var Zl={animationend:fl("Animation","AnimationEnd"),animationiteration:fl("Animation","AnimationIteration"),animationstart:fl("Animation","AnimationStart"),transitionrun:fl("Transition","TransitionRun"),transitionstart:fl("Transition","TransitionStart"),transitioncancel:fl("Transition","TransitionCancel"),transitionend:fl("Transition","TransitionEnd")},gc={},go={};ma&&(go=document.createElement("div").style,"AnimationEvent"in window||(delete Zl.animationend.animation,delete Zl.animationiteration.animation,delete Zl.animationstart.animation),"TransitionEvent"in window||delete Zl.transitionend.transition);function dl(t){if(gc[t])return gc[t];if(!Zl[t])return t;var e=Zl[t],a;for(a in e)if(e.hasOwnProperty(a)&&a in go)return gc[t]=e[a];return t}var mo=dl("animationend"),vo=dl("animationiteration"),po=dl("animationstart"),lm=dl("transitionrun"),nm=dl("transitionstart"),um=dl("transitioncancel"),yo=dl("transitionend"),bo=new Map,xo="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll scrollEnd toggle touchMove waiting wheel".split(" ");function Ie(t,e){bo.set(t,e),rl(e,[t])}var qe=[],Kl=0,mc=0;function Qu(){for(var t=Kl,e=mc=Kl=0;e<t;){var a=qe[e];qe[e++]=null;var l=qe[e];qe[e++]=null;var n=qe[e];qe[e++]=null;var u=qe[e];if(qe[e++]=null,l!==null&&n!==null){var c=l.pending;c===null?n.next=n:(n.next=c.next,c.next=n),l.pending=n}u!==0&&So(a,n,u)}}function Vu(t,e,a,l){qe[Kl++]=t,qe[Kl++]=e,qe[Kl++]=a,qe[Kl++]=l,mc|=l,t.lanes|=l,t=t.alternate,t!==null&&(t.lanes|=l)}function vc(t,e,a,l){return Vu(t,e,a,l),Zu(t)}function Ha(t,e){return Vu(t,null,null,e),Zu(t)}function So(t,e,a){t.lanes|=a;var l=t.alternate;l!==null&&(l.lanes|=a);for(var n=!1,u=t.return;u!==null;)u.childLanes|=a,l=u.alternate,l!==null&&(l.childLanes|=a),u.tag===22&&(t=u.stateNode,t===null||t._visibility&1||(n=!0)),t=u,u=u.return;n&&e!==null&&t.tag===3&&(u=t.stateNode,n=31-de(a),u=u.hiddenUpdates,t=u[n],t===null?u[n]=[e]:t.push(e),e.lane=a|536870912)}function Zu(t){if(50<iu)throw iu=0,As=null,Error(r(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var kl={},Ao=new WeakMap;function Ye(t,e){if(typeof t=="object"&&t!==null){var a=Ao.get(t);return a!==void 0?a:(e={value:t,source:e,stack:F(e)},Ao.set(t,e),e)}return{value:t,source:e,stack:F(e)}}var Jl=[],$l=0,Ku=null,ku=0,Ge=[],Le=0,hl=null,pa=1,ya="";function gl(t,e){Jl[$l++]=ku,Jl[$l++]=Ku,Ku=t,ku=e}function Eo(t,e,a){Ge[Le++]=pa,Ge[Le++]=ya,Ge[Le++]=hl,hl=t;var l=pa;t=ya;var n=32-de(l)-1;l&=~(1<<n),a+=1;var u=32-de(e)+n;if(30<u){var c=n-n%5;u=(l&(1<<c)-1).toString(32),l>>=c,n-=c,pa=1<<32-de(e)+n|a<<n|l,ya=u+t}else pa=1<<u|a<<n|l,ya=t}function pc(t){t.return!==null&&(gl(t,1),Eo(t,1,0))}function yc(t){for(;t===Ku;)Ku=Jl[--$l],Jl[$l]=null,ku=Jl[--$l],Jl[$l]=null;for(;t===hl;)hl=Ge[--Le],Ge[Le]=null,ya=Ge[--Le],Ge[Le]=null,pa=Ge[--Le],Ge[Le]=null}var he=null,ne=null,xt=!1,ta=null,sa=!1,bc=Error(r(519));function ml(t){var e=Error(r(418,""));throw qn(Ye(e,t)),bc}function Mo(t){var e=t.stateNode,a=t.type,l=t.memoizedProps;switch(e[Vt]=t,e[be]=l,a){case"dialog":vt("cancel",e),vt("close",e);break;case"iframe":case"object":case"embed":vt("load",e);break;case"video":case"audio":for(a=0;a<su.length;a++)vt(su[a],e);break;case"source":vt("error",e);break;case"img":case"image":case"link":vt("error",e),vt("load",e);break;case"details":vt("toggle",e);break;case"input":vt("invalid",e),qr(e,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),Uu(e);break;case"select":vt("invalid",e);break;case"textarea":vt("invalid",e),Gr(e,l.value,l.defaultValue,l.children),Uu(e)}a=l.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||e.textContent===""+a||l.suppressHydrationWarning===!0||Ud(e.textContent,a)?(l.popover!=null&&(vt("beforetoggle",e),vt("toggle",e)),l.onScroll!=null&&vt("scroll",e),l.onScrollEnd!=null&&vt("scrollend",e),l.onClick!=null&&(e.onclick=Ti),e=!0):e=!1,e||ml(t)}function To(t){for(he=t.return;he;)switch(he.tag){case 3:case 27:sa=!0;return;case 5:case 13:sa=!1;return;default:he=he.return}}function Hn(t){if(t!==he)return!1;if(!xt)return To(t),xt=!0,!1;var e=!1,a;if((a=t.tag!==3&&t.tag!==27)&&((a=t.tag===5)&&(a=t.type,a=!(a!=="form"&&a!=="button")||Ys(t.type,t.memoizedProps)),a=!a),a&&(e=!0),e&&ne&&ml(t),To(t),t.tag===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(r(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(a=t.data,a==="/$"){if(e===0){ne=aa(t.nextSibling);break t}e--}else a!=="$"&&a!=="$!"&&a!=="$?"||e++;t=t.nextSibling}ne=null}}else ne=he?aa(t.stateNode.nextSibling):null;return!0}function Bn(){ne=he=null,xt=!1}function qn(t){ta===null?ta=[t]:ta.push(t)}var Yn=Error(r(460)),Do=Error(r(474)),xc={then:function(){}};function zo(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Ju(){}function wo(t,e,a){switch(a=t[a],a===void 0?t.push(e):a!==e&&(e.then(Ju,Ju),e=a),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,t===Yn?Error(r(483)):t;default:if(typeof e.status=="string")e.then(Ju,Ju);else{if(t=Rt,t!==null&&100<t.shellSuspendCounter)throw Error(r(482));t=e,t.status="pending",t.then(function(l){if(e.status==="pending"){var n=e;n.status="fulfilled",n.value=l}},function(l){if(e.status==="pending"){var n=e;n.status="rejected",n.reason=l}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,t===Yn?Error(r(483)):t}throw Gn=e,Yn}}var Gn=null;function Ro(){if(Gn===null)throw Error(r(459));var t=Gn;return Gn=null,t}var Wl=null,Ln=0;function $u(t){var e=Ln;return Ln+=1,Wl===null&&(Wl=[]),wo(Wl,t,e)}function Xn(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function Wu(t,e){throw e.$$typeof===m?Error(r(525)):(t=Object.prototype.toString.call(e),Error(r(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function Oo(t){var e=t._init;return e(t._payload)}function No(t){function e(b,v){if(t){var A=b.deletions;A===null?(b.deletions=[v],b.flags|=16):A.push(v)}}function a(b,v){if(!t)return null;for(;v!==null;)e(b,v),v=v.sibling;return null}function l(b){for(var v=new Map;b!==null;)b.key!==null?v.set(b.key,b):v.set(b.index,b),b=b.sibling;return v}function n(b,v){return b=Ja(b,v),b.index=0,b.sibling=null,b}function u(b,v,A){return b.index=A,t?(A=b.alternate,A!==null?(A=A.index,A<v?(b.flags|=33554434,v):A):(b.flags|=33554434,v)):(b.flags|=1048576,v)}function c(b){return t&&b.alternate===null&&(b.flags|=33554434),b}function f(b,v,A,R){return v===null||v.tag!==6?(v=gs(A,b.mode,R),v.return=b,v):(v=n(v,A),v.return=b,v)}function d(b,v,A,R){var Q=A.type;return Q===x?D(b,v,A.props.children,R,A.key):v!==null&&(v.elementType===Q||typeof Q=="object"&&Q!==null&&Q.$$typeof===nt&&Oo(Q)===v.type)?(v=n(v,A.props),Xn(v,A),v.return=b,v):(v=gi(A.type,A.key,A.props,null,b.mode,R),Xn(v,A),v.return=b,v)}function y(b,v,A,R){return v===null||v.tag!==4||v.stateNode.containerInfo!==A.containerInfo||v.stateNode.implementation!==A.implementation?(v=ms(A,b.mode,R),v.return=b,v):(v=n(v,A.children||[]),v.return=b,v)}function D(b,v,A,R,Q){return v===null||v.tag!==7?(v=Tl(A,b.mode,R,Q),v.return=b,v):(v=n(v,A),v.return=b,v)}function C(b,v,A){if(typeof v=="string"&&v!==""||typeof v=="number"||typeof v=="bigint")return v=gs(""+v,b.mode,A),v.return=b,v;if(typeof v=="object"&&v!==null){switch(v.$$typeof){case S:return A=gi(v.type,v.key,v.props,null,b.mode,A),Xn(A,v),A.return=b,A;case M:return v=ms(v,b.mode,A),v.return=b,v;case nt:var R=v._init;return v=R(v._payload),C(b,v,A)}if(K(v)||B(v))return v=Tl(v,b.mode,A,null),v.return=b,v;if(typeof v.then=="function")return C(b,$u(v),A);if(v.$$typeof===X)return C(b,fi(b,v),A);Wu(b,v)}return null}function E(b,v,A,R){var Q=v!==null?v.key:null;if(typeof A=="string"&&A!==""||typeof A=="number"||typeof A=="bigint")return Q!==null?null:f(b,v,""+A,R);if(typeof A=="object"&&A!==null){switch(A.$$typeof){case S:return A.key===Q?d(b,v,A,R):null;case M:return A.key===Q?y(b,v,A,R):null;case nt:return Q=A._init,A=Q(A._payload),E(b,v,A,R)}if(K(A)||B(A))return Q!==null?null:D(b,v,A,R,null);if(typeof A.then=="function")return E(b,v,$u(A),R);if(A.$$typeof===X)return E(b,v,fi(b,A),R);Wu(b,A)}return null}function T(b,v,A,R,Q){if(typeof R=="string"&&R!==""||typeof R=="number"||typeof R=="bigint")return b=b.get(A)||null,f(v,b,""+R,Q);if(typeof R=="object"&&R!==null){switch(R.$$typeof){case S:return b=b.get(R.key===null?A:R.key)||null,d(v,b,R,Q);case M:return b=b.get(R.key===null?A:R.key)||null,y(v,b,R,Q);case nt:var ft=R._init;return R=ft(R._payload),T(b,v,A,R,Q)}if(K(R)||B(R))return b=b.get(A)||null,D(v,b,R,Q,null);if(typeof R.then=="function")return T(b,v,A,$u(R),Q);if(R.$$typeof===X)return T(b,v,A,fi(v,R),Q);Wu(v,R)}return null}function J(b,v,A,R){for(var Q=null,ft=null,P=v,et=v=0,ee=null;P!==null&&et<A.length;et++){P.index>et?(ee=P,P=null):ee=P.sibling;var St=E(b,P,A[et],R);if(St===null){P===null&&(P=ee);break}t&&P&&St.alternate===null&&e(b,P),v=u(St,v,et),ft===null?Q=St:ft.sibling=St,ft=St,P=ee}if(et===A.length)return a(b,P),xt&&gl(b,et),Q;if(P===null){for(;et<A.length;et++)P=C(b,A[et],R),P!==null&&(v=u(P,v,et),ft===null?Q=P:ft.sibling=P,ft=P);return xt&&gl(b,et),Q}for(P=l(P);et<A.length;et++)ee=T(P,b,et,A[et],R),ee!==null&&(t&&ee.alternate!==null&&P.delete(ee.key===null?et:ee.key),v=u(ee,v,et),ft===null?Q=ee:ft.sibling=ee,ft=ee);return t&&P.forEach(function(el){return e(b,el)}),xt&&gl(b,et),Q}function it(b,v,A,R){if(A==null)throw Error(r(151));for(var Q=null,ft=null,P=v,et=v=0,ee=null,St=A.next();P!==null&&!St.done;et++,St=A.next()){P.index>et?(ee=P,P=null):ee=P.sibling;var el=E(b,P,St.value,R);if(el===null){P===null&&(P=ee);break}t&&P&&el.alternate===null&&e(b,P),v=u(el,v,et),ft===null?Q=el:ft.sibling=el,ft=el,P=ee}if(St.done)return a(b,P),xt&&gl(b,et),Q;if(P===null){for(;!St.done;et++,St=A.next())St=C(b,St.value,R),St!==null&&(v=u(St,v,et),ft===null?Q=St:ft.sibling=St,ft=St);return xt&&gl(b,et),Q}for(P=l(P);!St.done;et++,St=A.next())St=T(P,b,et,St.value,R),St!==null&&(t&&St.alternate!==null&&P.delete(St.key===null?et:St.key),v=u(St,v,et),ft===null?Q=St:ft.sibling=St,ft=St);return t&&P.forEach(function(b0){return e(b,b0)}),xt&&gl(b,et),Q}function Gt(b,v,A,R){if(typeof A=="object"&&A!==null&&A.type===x&&A.key===null&&(A=A.props.children),typeof A=="object"&&A!==null){switch(A.$$typeof){case S:t:{for(var Q=A.key;v!==null;){if(v.key===Q){if(Q=A.type,Q===x){if(v.tag===7){a(b,v.sibling),R=n(v,A.props.children),R.return=b,b=R;break t}}else if(v.elementType===Q||typeof Q=="object"&&Q!==null&&Q.$$typeof===nt&&Oo(Q)===v.type){a(b,v.sibling),R=n(v,A.props),Xn(R,A),R.return=b,b=R;break t}a(b,v);break}else e(b,v);v=v.sibling}A.type===x?(R=Tl(A.props.children,b.mode,R,A.key),R.return=b,b=R):(R=gi(A.type,A.key,A.props,null,b.mode,R),Xn(R,A),R.return=b,b=R)}return c(b);case M:t:{for(Q=A.key;v!==null;){if(v.key===Q)if(v.tag===4&&v.stateNode.containerInfo===A.containerInfo&&v.stateNode.implementation===A.implementation){a(b,v.sibling),R=n(v,A.children||[]),R.return=b,b=R;break t}else{a(b,v);break}else e(b,v);v=v.sibling}R=ms(A,b.mode,R),R.return=b,b=R}return c(b);case nt:return Q=A._init,A=Q(A._payload),Gt(b,v,A,R)}if(K(A))return J(b,v,A,R);if(B(A)){if(Q=B(A),typeof Q!="function")throw Error(r(150));return A=Q.call(A),it(b,v,A,R)}if(typeof A.then=="function")return Gt(b,v,$u(A),R);if(A.$$typeof===X)return Gt(b,v,fi(b,A),R);Wu(b,A)}return typeof A=="string"&&A!==""||typeof A=="number"||typeof A=="bigint"?(A=""+A,v!==null&&v.tag===6?(a(b,v.sibling),R=n(v,A),R.return=b,b=R):(a(b,v),R=gs(A,b.mode,R),R.return=b,b=R),c(b)):a(b,v)}return function(b,v,A,R){try{Ln=0;var Q=Gt(b,v,A,R);return Wl=null,Q}catch(P){if(P===Yn)throw P;var ft=Ze(29,P,null,b.mode);return ft.lanes=R,ft.return=b,ft}finally{}}}var vl=No(!0),_o=No(!1),Fl=bt(null),Fu=bt(0);function Co(t,e){t=Ra,gt(Fu,t),gt(Fl,e),Ra=t|e.baseLanes}function Sc(){gt(Fu,Ra),gt(Fl,Fl.current)}function Ac(){Ra=Fu.current,Ut(Fl),Ut(Fu)}var Xe=bt(null),ra=null;function Ba(t){var e=t.alternate;gt($t,$t.current&1),gt(Xe,t),ra===null&&(e===null||Fl.current!==null||e.memoizedState!==null)&&(ra=t)}function Uo(t){if(t.tag===22){if(gt($t,$t.current),gt(Xe,t),ra===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(ra=t)}}else qa()}function qa(){gt($t,$t.current),gt(Xe,Xe.current)}function ba(t){Ut(Xe),ra===t&&(ra=null),Ut($t)}var $t=bt(0);function Pu(t){for(var e=t;e!==null;){if(e.tag===13){var a=e.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||a.data==="$!"))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}var im=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(a,l){t.push(l)}};this.abort=function(){e.aborted=!0,t.forEach(function(a){return a()})}},cm=i.unstable_scheduleCallback,sm=i.unstable_NormalPriority,Wt={$$typeof:X,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Ec(){return{controller:new im,data:new Map,refCount:0}}function Qn(t){t.refCount--,t.refCount===0&&cm(sm,function(){t.controller.abort()})}var Vn=null,Mc=0,Pl=0,Il=null;function rm(t,e){if(Vn===null){var a=Vn=[];Mc=0,Pl=Os(),Il={status:"pending",value:void 0,then:function(l){a.push(l)}}}return Mc++,e.then(jo,jo),e}function jo(){if(--Mc===0&&Vn!==null){Il!==null&&(Il.status="fulfilled");var t=Vn;Vn=null,Pl=0,Il=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function om(t,e){var a=[],l={status:"pending",value:null,reason:null,then:function(n){a.push(n)}};return t.then(function(){l.status="fulfilled",l.value=e;for(var n=0;n<a.length;n++)(0,a[n])(e)},function(n){for(l.status="rejected",l.reason=n,n=0;n<a.length;n++)(0,a[n])(void 0)}),l}var Ho=Y.S;Y.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&rm(t,e),Ho!==null&&Ho(t,e)};var pl=bt(null);function Tc(){var t=pl.current;return t!==null?t:Rt.pooledCache}function Iu(t,e){e===null?gt(pl,pl.current):gt(pl,e.pool)}function Bo(){var t=Tc();return t===null?null:{parent:Wt._currentValue,pool:t}}var Ya=0,ot=null,Tt=null,Zt=null,ti=!1,tn=!1,yl=!1,ei=0,Zn=0,en=null,fm=0;function Qt(){throw Error(r(321))}function Dc(t,e){if(e===null)return!1;for(var a=0;a<e.length&&a<t.length;a++)if(!we(t[a],e[a]))return!1;return!0}function zc(t,e,a,l,n,u){return Ya=u,ot=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,Y.H=t===null||t.memoizedState===null?bl:Ga,yl=!1,u=a(l,n),yl=!1,tn&&(u=Yo(e,a,l,n)),qo(t),u}function qo(t){Y.H=oa;var e=Tt!==null&&Tt.next!==null;if(Ya=0,Zt=Tt=ot=null,ti=!1,Zn=0,en=null,e)throw Error(r(300));t===null||It||(t=t.dependencies,t!==null&&oi(t)&&(It=!0))}function Yo(t,e,a,l){ot=t;var n=0;do{if(tn&&(en=null),Zn=0,tn=!1,25<=n)throw Error(r(301));if(n+=1,Zt=Tt=null,t.updateQueue!=null){var u=t.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}Y.H=xl,u=e(a,l)}while(tn);return u}function dm(){var t=Y.H,e=t.useState()[0];return e=typeof e.then=="function"?Kn(e):e,t=t.useState()[0],(Tt!==null?Tt.memoizedState:null)!==t&&(ot.flags|=1024),e}function wc(){var t=ei!==0;return ei=0,t}function Rc(t,e,a){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~a}function Oc(t){if(ti){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}ti=!1}Ya=0,Zt=Tt=ot=null,tn=!1,Zn=ei=0,en=null}function Se(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Zt===null?ot.memoizedState=Zt=t:Zt=Zt.next=t,Zt}function Kt(){if(Tt===null){var t=ot.alternate;t=t!==null?t.memoizedState:null}else t=Tt.next;var e=Zt===null?ot.memoizedState:Zt.next;if(e!==null)Zt=e,Tt=t;else{if(t===null)throw ot.alternate===null?Error(r(467)):Error(r(310));Tt=t,t={memoizedState:Tt.memoizedState,baseState:Tt.baseState,baseQueue:Tt.baseQueue,queue:Tt.queue,next:null},Zt===null?ot.memoizedState=Zt=t:Zt=Zt.next=t}return Zt}var ai;ai=function(){return{lastEffect:null,events:null,stores:null,memoCache:null}};function Kn(t){var e=Zn;return Zn+=1,en===null&&(en=[]),t=wo(en,t,e),e=ot,(Zt===null?e.memoizedState:Zt.next)===null&&(e=e.alternate,Y.H=e===null||e.memoizedState===null?bl:Ga),t}function li(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Kn(t);if(t.$$typeof===X)return se(t)}throw Error(r(438,String(t)))}function Nc(t){var e=null,a=ot.updateQueue;if(a!==null&&(e=a.memoCache),e==null){var l=ot.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(e={data:l.data.map(function(n){return n.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),a===null&&(a=ai(),ot.updateQueue=a),a.memoCache=e,a=e.data[e.index],a===void 0)for(a=e.data[e.index]=Array(t),l=0;l<t;l++)a[l]=At;return e.index++,a}function xa(t,e){return typeof e=="function"?e(t):e}function ni(t){var e=Kt();return _c(e,Tt,t)}function _c(t,e,a){var l=t.queue;if(l===null)throw Error(r(311));l.lastRenderedReducer=a;var n=t.baseQueue,u=l.pending;if(u!==null){if(n!==null){var c=n.next;n.next=u.next,u.next=c}e.baseQueue=n=u,l.pending=null}if(u=t.baseState,n===null)t.memoizedState=u;else{e=n.next;var f=c=null,d=null,y=e,D=!1;do{var C=y.lane&-536870913;if(C!==y.lane?(yt&C)===C:(Ya&C)===C){var E=y.revertLane;if(E===0)d!==null&&(d=d.next={lane:0,revertLane:0,action:y.action,hasEagerState:y.hasEagerState,eagerState:y.eagerState,next:null}),C===Pl&&(D=!0);else if((Ya&E)===E){y=y.next,E===Pl&&(D=!0);continue}else C={lane:0,revertLane:y.revertLane,action:y.action,hasEagerState:y.hasEagerState,eagerState:y.eagerState,next:null},d===null?(f=d=C,c=u):d=d.next=C,ot.lanes|=E,$a|=E;C=y.action,yl&&a(u,C),u=y.hasEagerState?y.eagerState:a(u,C)}else E={lane:C,revertLane:y.revertLane,action:y.action,hasEagerState:y.hasEagerState,eagerState:y.eagerState,next:null},d===null?(f=d=E,c=u):d=d.next=E,ot.lanes|=C,$a|=C;y=y.next}while(y!==null&&y!==e);if(d===null?c=u:d.next=f,!we(u,t.memoizedState)&&(It=!0,D&&(a=Il,a!==null)))throw a;t.memoizedState=u,t.baseState=c,t.baseQueue=d,l.lastRenderedState=u}return n===null&&(l.lanes=0),[t.memoizedState,l.dispatch]}function Cc(t){var e=Kt(),a=e.queue;if(a===null)throw Error(r(311));a.lastRenderedReducer=t;var l=a.dispatch,n=a.pending,u=e.memoizedState;if(n!==null){a.pending=null;var c=n=n.next;do u=t(u,c.action),c=c.next;while(c!==n);we(u,e.memoizedState)||(It=!0),e.memoizedState=u,e.baseQueue===null&&(e.baseState=u),a.lastRenderedState=u}return[u,l]}function Go(t,e,a){var l=ot,n=Kt(),u=xt;if(u){if(a===void 0)throw Error(r(407));a=a()}else a=e();var c=!we((Tt||n).memoizedState,a);if(c&&(n.memoizedState=a,It=!0),n=n.queue,Hc(Qo.bind(null,l,n,t),[t]),n.getSnapshot!==e||c||Zt!==null&&Zt.memoizedState.tag&1){if(l.flags|=2048,an(9,Xo.bind(null,l,n,a,e),{destroy:void 0},null),Rt===null)throw Error(r(349));u||(Ya&60)!==0||Lo(l,e,a)}return a}function Lo(t,e,a){t.flags|=16384,t={getSnapshot:e,value:a},e=ot.updateQueue,e===null?(e=ai(),ot.updateQueue=e,e.stores=[t]):(a=e.stores,a===null?e.stores=[t]:a.push(t))}function Xo(t,e,a,l){e.value=a,e.getSnapshot=l,Vo(e)&&Zo(t)}function Qo(t,e,a){return a(function(){Vo(e)&&Zo(t)})}function Vo(t){var e=t.getSnapshot;t=t.value;try{var a=e();return!we(t,a)}catch{return!0}}function Zo(t){var e=Ha(t,2);e!==null&&ge(e,t,2)}function Uc(t){var e=Se();if(typeof t=="function"){var a=t;if(t=a(),yl){Pe(!0);try{a()}finally{Pe(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:xa,lastRenderedState:t},e}function Ko(t,e,a,l){return t.baseState=a,_c(t,Tt,typeof l=="function"?l:xa)}function hm(t,e,a,l,n){if(ci(t))throw Error(r(485));if(t=e.action,t!==null){var u={payload:n,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(c){u.listeners.push(c)}};Y.T!==null?a(!0):u.isTransition=!1,l(u),a=e.pending,a===null?(u.next=e.pending=u,ko(e,u)):(u.next=a.next,e.pending=a.next=u)}}function ko(t,e){var a=e.action,l=e.payload,n=t.state;if(e.isTransition){var u=Y.T,c={};Y.T=c;try{var f=a(n,l),d=Y.S;d!==null&&d(c,f),Jo(t,e,f)}catch(y){jc(t,e,y)}finally{Y.T=u}}else try{u=a(n,l),Jo(t,e,u)}catch(y){jc(t,e,y)}}function Jo(t,e,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(l){$o(t,e,l)},function(l){return jc(t,e,l)}):$o(t,e,a)}function $o(t,e,a){e.status="fulfilled",e.value=a,Wo(e),t.state=a,e=t.pending,e!==null&&(a=e.next,a===e?t.pending=null:(a=a.next,e.next=a,ko(t,a)))}function jc(t,e,a){var l=t.pending;if(t.pending=null,l!==null){l=l.next;do e.status="rejected",e.reason=a,Wo(e),e=e.next;while(e!==l)}t.action=null}function Wo(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function Fo(t,e){return e}function Po(t,e){if(xt){var a=Rt.formState;if(a!==null){t:{var l=ot;if(xt){if(ne){e:{for(var n=ne,u=sa;n.nodeType!==8;){if(!u){n=null;break e}if(n=aa(n.nextSibling),n===null){n=null;break e}}u=n.data,n=u==="F!"||u==="F"?n:null}if(n){ne=aa(n.nextSibling),l=n.data==="F!";break t}}ml(l)}l=!1}l&&(e=a[0])}}return a=Se(),a.memoizedState=a.baseState=e,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Fo,lastRenderedState:e},a.queue=l,a=pf.bind(null,ot,l),l.dispatch=a,l=Uc(!1),u=Lc.bind(null,ot,!1,l.queue),l=Se(),n={state:e,dispatch:null,action:t,pending:null},l.queue=n,a=hm.bind(null,ot,n,u,a),n.dispatch=a,l.memoizedState=t,[e,a,!1]}function Io(t){var e=Kt();return tf(e,Tt,t)}function tf(t,e,a){e=_c(t,e,Fo)[0],t=ni(xa)[0],e=typeof e=="object"&&e!==null&&typeof e.then=="function"?Kn(e):e;var l=Kt(),n=l.queue,u=n.dispatch;return a!==l.memoizedState&&(ot.flags|=2048,an(9,gm.bind(null,n,a),{destroy:void 0},null)),[e,u,t]}function gm(t,e){t.action=e}function ef(t){var e=Kt(),a=Tt;if(a!==null)return tf(e,a,t);Kt(),e=e.memoizedState,a=Kt();var l=a.queue.dispatch;return a.memoizedState=t,[e,l,!1]}function an(t,e,a,l){return t={tag:t,create:e,inst:a,deps:l,next:null},e=ot.updateQueue,e===null&&(e=ai(),ot.updateQueue=e),a=e.lastEffect,a===null?e.lastEffect=t.next=t:(l=a.next,a.next=t,t.next=l,e.lastEffect=t),t}function af(){return Kt().memoizedState}function ui(t,e,a,l){var n=Se();ot.flags|=t,n.memoizedState=an(1|e,a,{destroy:void 0},l===void 0?null:l)}function ii(t,e,a,l){var n=Kt();l=l===void 0?null:l;var u=n.memoizedState.inst;Tt!==null&&l!==null&&Dc(l,Tt.memoizedState.deps)?n.memoizedState=an(e,a,u,l):(ot.flags|=t,n.memoizedState=an(1|e,a,u,l))}function lf(t,e){ui(8390656,8,t,e)}function Hc(t,e){ii(2048,8,t,e)}function nf(t,e){return ii(4,2,t,e)}function uf(t,e){return ii(4,4,t,e)}function cf(t,e){if(typeof e=="function"){t=t();var a=e(t);return function(){typeof a=="function"?a():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function sf(t,e,a){a=a!=null?a.concat([t]):null,ii(4,4,cf.bind(null,e,t),a)}function Bc(){}function rf(t,e){var a=Kt();e=e===void 0?null:e;var l=a.memoizedState;return e!==null&&Dc(e,l[1])?l[0]:(a.memoizedState=[t,e],t)}function of(t,e){var a=Kt();e=e===void 0?null:e;var l=a.memoizedState;if(e!==null&&Dc(e,l[1]))return l[0];if(l=t(),yl){Pe(!0);try{t()}finally{Pe(!1)}}return a.memoizedState=[l,e],l}function qc(t,e,a){return a===void 0||(Ya&1073741824)!==0?t.memoizedState=e:(t.memoizedState=a,t=dd(),ot.lanes|=t,$a|=t,a)}function ff(t,e,a,l){return we(a,e)?a:Fl.current!==null?(t=qc(t,a,l),we(t,e)||(It=!0),t):(Ya&42)===0?(It=!0,t.memoizedState=a):(t=dd(),ot.lanes|=t,$a|=t,e)}function df(t,e,a,l,n){var u=q.p;q.p=u!==0&&8>u?u:8;var c=Y.T,f={};Y.T=f,Lc(t,!1,e,a);try{var d=n(),y=Y.S;if(y!==null&&y(f,d),d!==null&&typeof d=="object"&&typeof d.then=="function"){var D=om(d,l);kn(t,e,D,_e(t))}else kn(t,e,l,_e(t))}catch(C){kn(t,e,{then:function(){},status:"rejected",reason:C},_e())}finally{q.p=u,Y.T=c}}function mm(){}function Yc(t,e,a,l){if(t.tag!==5)throw Error(r(476));var n=hf(t).queue;df(t,n,e,ct,a===null?mm:function(){return gf(t),a(l)})}function hf(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:ct,baseState:ct,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:xa,lastRenderedState:ct},next:null};var a={};return e.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:xa,lastRenderedState:a},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function gf(t){var e=hf(t).next.queue;kn(t,e,{},_e())}function Gc(){return se(hu)}function mf(){return Kt().memoizedState}function vf(){return Kt().memoizedState}function vm(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var a=_e();t=Qa(a);var l=Va(e,t,a);l!==null&&(ge(l,e,a),Wn(l,e,a)),e={cache:Ec()},t.payload=e;return}e=e.return}}function pm(t,e,a){var l=_e();a={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},ci(t)?yf(e,a):(a=vc(t,e,a,l),a!==null&&(ge(a,t,l),bf(a,e,l)))}function pf(t,e,a){var l=_e();kn(t,e,a,l)}function kn(t,e,a,l){var n={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(ci(t))yf(e,n);else{var u=t.alternate;if(t.lanes===0&&(u===null||u.lanes===0)&&(u=e.lastRenderedReducer,u!==null))try{var c=e.lastRenderedState,f=u(c,a);if(n.hasEagerState=!0,n.eagerState=f,we(f,c))return Vu(t,e,n,0),Rt===null&&Qu(),!1}catch{}finally{}if(a=vc(t,e,n,l),a!==null)return ge(a,t,l),bf(a,e,l),!0}return!1}function Lc(t,e,a,l){if(l={lane:2,revertLane:Os(),action:l,hasEagerState:!1,eagerState:null,next:null},ci(t)){if(e)throw Error(r(479))}else e=vc(t,a,l,2),e!==null&&ge(e,t,2)}function ci(t){var e=t.alternate;return t===ot||e!==null&&e===ot}function yf(t,e){tn=ti=!0;var a=t.pending;a===null?e.next=e:(e.next=a.next,a.next=e),t.pending=e}function bf(t,e,a){if((a&4194176)!==0){var l=e.lanes;l&=t.pendingLanes,a|=l,e.lanes=a,je(t,a)}}var oa={readContext:se,use:li,useCallback:Qt,useContext:Qt,useEffect:Qt,useImperativeHandle:Qt,useLayoutEffect:Qt,useInsertionEffect:Qt,useMemo:Qt,useReducer:Qt,useRef:Qt,useState:Qt,useDebugValue:Qt,useDeferredValue:Qt,useTransition:Qt,useSyncExternalStore:Qt,useId:Qt};oa.useCacheRefresh=Qt,oa.useMemoCache=Qt,oa.useHostTransitionStatus=Qt,oa.useFormState=Qt,oa.useActionState=Qt,oa.useOptimistic=Qt;var bl={readContext:se,use:li,useCallback:function(t,e){return Se().memoizedState=[t,e===void 0?null:e],t},useContext:se,useEffect:lf,useImperativeHandle:function(t,e,a){a=a!=null?a.concat([t]):null,ui(4194308,4,cf.bind(null,e,t),a)},useLayoutEffect:function(t,e){return ui(4194308,4,t,e)},useInsertionEffect:function(t,e){ui(4,2,t,e)},useMemo:function(t,e){var a=Se();e=e===void 0?null:e;var l=t();if(yl){Pe(!0);try{t()}finally{Pe(!1)}}return a.memoizedState=[l,e],l},useReducer:function(t,e,a){var l=Se();if(a!==void 0){var n=a(e);if(yl){Pe(!0);try{a(e)}finally{Pe(!1)}}}else n=e;return l.memoizedState=l.baseState=n,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:n},l.queue=t,t=t.dispatch=pm.bind(null,ot,t),[l.memoizedState,t]},useRef:function(t){var e=Se();return t={current:t},e.memoizedState=t},useState:function(t){t=Uc(t);var e=t.queue,a=pf.bind(null,ot,e);return e.dispatch=a,[t.memoizedState,a]},useDebugValue:Bc,useDeferredValue:function(t,e){var a=Se();return qc(a,t,e)},useTransition:function(){var t=Uc(!1);return t=df.bind(null,ot,t.queue,!0,!1),Se().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,a){var l=ot,n=Se();if(xt){if(a===void 0)throw Error(r(407));a=a()}else{if(a=e(),Rt===null)throw Error(r(349));(yt&60)!==0||Lo(l,e,a)}n.memoizedState=a;var u={value:a,getSnapshot:e};return n.queue=u,lf(Qo.bind(null,l,u,t),[t]),l.flags|=2048,an(9,Xo.bind(null,l,u,a,e),{destroy:void 0},null),a},useId:function(){var t=Se(),e=Rt.identifierPrefix;if(xt){var a=ya,l=pa;a=(l&~(1<<32-de(l)-1)).toString(32)+a,e=":"+e+"R"+a,a=ei++,0<a&&(e+="H"+a.toString(32)),e+=":"}else a=fm++,e=":"+e+"r"+a.toString(32)+":";return t.memoizedState=e},useCacheRefresh:function(){return Se().memoizedState=vm.bind(null,ot)}};bl.useMemoCache=Nc,bl.useHostTransitionStatus=Gc,bl.useFormState=Po,bl.useActionState=Po,bl.useOptimistic=function(t){var e=Se();e.memoizedState=e.baseState=t;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=a,e=Lc.bind(null,ot,!0,a),a.dispatch=e,[t,e]};var Ga={readContext:se,use:li,useCallback:rf,useContext:se,useEffect:Hc,useImperativeHandle:sf,useInsertionEffect:nf,useLayoutEffect:uf,useMemo:of,useReducer:ni,useRef:af,useState:function(){return ni(xa)},useDebugValue:Bc,useDeferredValue:function(t,e){var a=Kt();return ff(a,Tt.memoizedState,t,e)},useTransition:function(){var t=ni(xa)[0],e=Kt().memoizedState;return[typeof t=="boolean"?t:Kn(t),e]},useSyncExternalStore:Go,useId:mf};Ga.useCacheRefresh=vf,Ga.useMemoCache=Nc,Ga.useHostTransitionStatus=Gc,Ga.useFormState=Io,Ga.useActionState=Io,Ga.useOptimistic=function(t,e){var a=Kt();return Ko(a,Tt,t,e)};var xl={readContext:se,use:li,useCallback:rf,useContext:se,useEffect:Hc,useImperativeHandle:sf,useInsertionEffect:nf,useLayoutEffect:uf,useMemo:of,useReducer:Cc,useRef:af,useState:function(){return Cc(xa)},useDebugValue:Bc,useDeferredValue:function(t,e){var a=Kt();return Tt===null?qc(a,t,e):ff(a,Tt.memoizedState,t,e)},useTransition:function(){var t=Cc(xa)[0],e=Kt().memoizedState;return[typeof t=="boolean"?t:Kn(t),e]},useSyncExternalStore:Go,useId:mf};xl.useCacheRefresh=vf,xl.useMemoCache=Nc,xl.useHostTransitionStatus=Gc,xl.useFormState=ef,xl.useActionState=ef,xl.useOptimistic=function(t,e){var a=Kt();return Tt!==null?Ko(a,Tt,t,e):(a.baseState=t,[t,a.queue.dispatch])};function Xc(t,e,a,l){e=t.memoizedState,a=a(l,e),a=a==null?e:$({},e,a),t.memoizedState=a,t.lanes===0&&(t.updateQueue.baseState=a)}var Qc={isMounted:function(t){return(t=t._reactInternals)?L(t)===t:!1},enqueueSetState:function(t,e,a){t=t._reactInternals;var l=_e(),n=Qa(l);n.payload=e,a!=null&&(n.callback=a),e=Va(t,n,l),e!==null&&(ge(e,t,l),Wn(e,t,l))},enqueueReplaceState:function(t,e,a){t=t._reactInternals;var l=_e(),n=Qa(l);n.tag=1,n.payload=e,a!=null&&(n.callback=a),e=Va(t,n,l),e!==null&&(ge(e,t,l),Wn(e,t,l))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var a=_e(),l=Qa(a);l.tag=2,e!=null&&(l.callback=e),e=Va(t,l,a),e!==null&&(ge(e,t,a),Wn(e,t,a))}};function xf(t,e,a,l,n,u,c){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(l,u,c):e.prototype&&e.prototype.isPureReactComponent?!Un(a,l)||!Un(n,u):!0}function Sf(t,e,a,l){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(a,l),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(a,l),e.state!==t&&Qc.enqueueReplaceState(e,e.state,null)}function Sl(t,e){var a=e;if("ref"in e){a={};for(var l in e)l!=="ref"&&(a[l]=e[l])}if(t=t.defaultProps){a===e&&(a=$({},a));for(var n in t)a[n]===void 0&&(a[n]=t[n])}return a}var si=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Af(t){si(t)}function Ef(t){console.error(t)}function Mf(t){si(t)}function ri(t,e){try{var a=t.onUncaughtError;a(e.value,{componentStack:e.stack})}catch(l){setTimeout(function(){throw l})}}function Tf(t,e,a){try{var l=t.onCaughtError;l(a.value,{componentStack:a.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function Vc(t,e,a){return a=Qa(a),a.tag=3,a.payload={element:null},a.callback=function(){ri(t,e)},a}function Df(t){return t=Qa(t),t.tag=3,t}function zf(t,e,a,l){var n=a.type.getDerivedStateFromError;if(typeof n=="function"){var u=l.value;t.payload=function(){return n(u)},t.callback=function(){Tf(e,a,l)}}var c=a.stateNode;c!==null&&typeof c.componentDidCatch=="function"&&(t.callback=function(){Tf(e,a,l),typeof n!="function"&&(Wa===null?Wa=new Set([this]):Wa.add(this));var f=l.stack;this.componentDidCatch(l.value,{componentStack:f!==null?f:""})})}function ym(t,e,a,l,n){if(a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(e=a.alternate,e!==null&&$n(e,a,n,!0),a=Xe.current,a!==null){switch(a.tag){case 13:return ra===null?Ts():a.alternate===null&&Yt===0&&(Yt=3),a.flags&=-257,a.flags|=65536,a.lanes=n,l===xc?a.flags|=16384:(e=a.updateQueue,e===null?a.updateQueue=new Set([l]):e.add(l),zs(t,l,n)),!1;case 22:return a.flags|=65536,l===xc?a.flags|=16384:(e=a.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([l])},a.updateQueue=e):(a=e.retryQueue,a===null?e.retryQueue=new Set([l]):a.add(l)),zs(t,l,n)),!1}throw Error(r(435,a.tag))}return zs(t,l,n),Ts(),!1}if(xt)return e=Xe.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=n,l!==bc&&(t=Error(r(422),{cause:l}),qn(Ye(t,a)))):(l!==bc&&(e=Error(r(423),{cause:l}),qn(Ye(e,a))),t=t.current.alternate,t.flags|=65536,n&=-n,t.lanes|=n,l=Ye(l,a),n=Vc(t.stateNode,l,n),us(t,n),Yt!==4&&(Yt=2)),!1;var u=Error(r(520),{cause:l});if(u=Ye(u,a),nu===null?nu=[u]:nu.push(u),Yt!==4&&(Yt=2),e===null)return!0;l=Ye(l,a),a=e;do{switch(a.tag){case 3:return a.flags|=65536,t=n&-n,a.lanes|=t,t=Vc(a.stateNode,l,t),us(a,t),!1;case 1:if(e=a.type,u=a.stateNode,(a.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(Wa===null||!Wa.has(u))))return a.flags|=65536,n&=-n,a.lanes|=n,n=Df(n),zf(n,t,a,l),us(a,n),!1}a=a.return}while(a!==null);return!1}var wf=Error(r(461)),It=!1;function ue(t,e,a,l){e.child=t===null?_o(e,null,a,l):vl(e,t.child,a,l)}function Rf(t,e,a,l,n){a=a.render;var u=e.ref;if("ref"in l){var c={};for(var f in l)f!=="ref"&&(c[f]=l[f])}else c=l;return El(e),l=zc(t,e,a,c,u,n),f=wc(),t!==null&&!It?(Rc(t,e,n),Sa(t,e,n)):(xt&&f&&pc(e),e.flags|=1,ue(t,e,l,n),e.child)}function Of(t,e,a,l,n){if(t===null){var u=a.type;return typeof u=="function"&&!hs(u)&&u.defaultProps===void 0&&a.compare===null?(e.tag=15,e.type=u,Nf(t,e,u,l,n)):(t=gi(a.type,null,l,e,e.mode,n),t.ref=e.ref,t.return=e,e.child=t)}if(u=t.child,!Ic(t,n)){var c=u.memoizedProps;if(a=a.compare,a=a!==null?a:Un,a(c,l)&&t.ref===e.ref)return Sa(t,e,n)}return e.flags|=1,t=Ja(u,l),t.ref=e.ref,t.return=e,e.child=t}function Nf(t,e,a,l,n){if(t!==null){var u=t.memoizedProps;if(Un(u,l)&&t.ref===e.ref)if(It=!1,e.pendingProps=l=u,Ic(t,n))(t.flags&131072)!==0&&(It=!0);else return e.lanes=t.lanes,Sa(t,e,n)}return Zc(t,e,a,l,n)}function _f(t,e,a){var l=e.pendingProps,n=l.children,u=(e.stateNode._pendingVisibility&2)!==0,c=t!==null?t.memoizedState:null;if(Jn(t,e),l.mode==="hidden"||u){if((e.flags&128)!==0){if(l=c!==null?c.baseLanes|a:a,t!==null){for(n=e.child=t.child,u=0;n!==null;)u=u|n.lanes|n.childLanes,n=n.sibling;e.childLanes=u&~l}else e.childLanes=0,e.child=null;return Cf(t,e,l,a)}if((a&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&Iu(e,c!==null?c.cachePool:null),c!==null?Co(e,c):Sc(),Uo(e);else return e.lanes=e.childLanes=536870912,Cf(t,e,c!==null?c.baseLanes|a:a,a)}else c!==null?(Iu(e,c.cachePool),Co(e,c),qa(),e.memoizedState=null):(t!==null&&Iu(e,null),Sc(),qa());return ue(t,e,n,a),e.child}function Cf(t,e,a,l){var n=Tc();return n=n===null?null:{parent:Wt._currentValue,pool:n},e.memoizedState={baseLanes:a,cachePool:n},t!==null&&Iu(e,null),Sc(),Uo(e),t!==null&&$n(t,e,l,!0),null}function Jn(t,e){var a=e.ref;if(a===null)t!==null&&t.ref!==null&&(e.flags|=2097664);else{if(typeof a!="function"&&typeof a!="object")throw Error(r(284));(t===null||t.ref!==a)&&(e.flags|=2097664)}}function Zc(t,e,a,l,n){return El(e),a=zc(t,e,a,l,void 0,n),l=wc(),t!==null&&!It?(Rc(t,e,n),Sa(t,e,n)):(xt&&l&&pc(e),e.flags|=1,ue(t,e,a,n),e.child)}function Uf(t,e,a,l,n,u){return El(e),e.updateQueue=null,a=Yo(e,l,a,n),qo(t),l=wc(),t!==null&&!It?(Rc(t,e,u),Sa(t,e,u)):(xt&&l&&pc(e),e.flags|=1,ue(t,e,a,u),e.child)}function jf(t,e,a,l,n){if(El(e),e.stateNode===null){var u=kl,c=a.contextType;typeof c=="object"&&c!==null&&(u=se(c)),u=new a(l,u),e.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=Qc,e.stateNode=u,u._reactInternals=e,u=e.stateNode,u.props=l,u.state=e.memoizedState,u.refs={},ls(e),c=a.contextType,u.context=typeof c=="object"&&c!==null?se(c):kl,u.state=e.memoizedState,c=a.getDerivedStateFromProps,typeof c=="function"&&(Xc(e,a,c,l),u.state=e.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(c=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),c!==u.state&&Qc.enqueueReplaceState(u,u.state,null),Pn(e,l,u,n),Fn(),u.state=e.memoizedState),typeof u.componentDidMount=="function"&&(e.flags|=4194308),l=!0}else if(t===null){u=e.stateNode;var f=e.memoizedProps,d=Sl(a,f);u.props=d;var y=u.context,D=a.contextType;c=kl,typeof D=="object"&&D!==null&&(c=se(D));var C=a.getDerivedStateFromProps;D=typeof C=="function"||typeof u.getSnapshotBeforeUpdate=="function",f=e.pendingProps!==f,D||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(f||y!==c)&&Sf(e,u,l,c),Xa=!1;var E=e.memoizedState;u.state=E,Pn(e,l,u,n),Fn(),y=e.memoizedState,f||E!==y||Xa?(typeof C=="function"&&(Xc(e,a,C,l),y=e.memoizedState),(d=Xa||xf(e,a,d,l,E,y,c))?(D||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(e.flags|=4194308)):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=l,e.memoizedState=y),u.props=l,u.state=y,u.context=c,l=d):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),l=!1)}else{u=e.stateNode,ns(t,e),c=e.memoizedProps,D=Sl(a,c),u.props=D,C=e.pendingProps,E=u.context,y=a.contextType,d=kl,typeof y=="object"&&y!==null&&(d=se(y)),f=a.getDerivedStateFromProps,(y=typeof f=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(c!==C||E!==d)&&Sf(e,u,l,d),Xa=!1,E=e.memoizedState,u.state=E,Pn(e,l,u,n),Fn();var T=e.memoizedState;c!==C||E!==T||Xa||t!==null&&t.dependencies!==null&&oi(t.dependencies)?(typeof f=="function"&&(Xc(e,a,f,l),T=e.memoizedState),(D=Xa||xf(e,a,D,l,E,T,d)||t!==null&&t.dependencies!==null&&oi(t.dependencies))?(y||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(l,T,d),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(l,T,d)),typeof u.componentDidUpdate=="function"&&(e.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof u.componentDidUpdate!="function"||c===t.memoizedProps&&E===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||c===t.memoizedProps&&E===t.memoizedState||(e.flags|=1024),e.memoizedProps=l,e.memoizedState=T),u.props=l,u.state=T,u.context=d,l=D):(typeof u.componentDidUpdate!="function"||c===t.memoizedProps&&E===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||c===t.memoizedProps&&E===t.memoizedState||(e.flags|=1024),l=!1)}return u=l,Jn(t,e),l=(e.flags&128)!==0,u||l?(u=e.stateNode,a=l&&typeof a.getDerivedStateFromError!="function"?null:u.render(),e.flags|=1,t!==null&&l?(e.child=vl(e,t.child,null,n),e.child=vl(e,null,a,n)):ue(t,e,a,n),e.memoizedState=u.state,t=e.child):t=Sa(t,e,n),t}function Hf(t,e,a,l){return Bn(),e.flags|=256,ue(t,e,a,l),e.child}var Kc={dehydrated:null,treeContext:null,retryLane:0};function kc(t){return{baseLanes:t,cachePool:Bo()}}function Jc(t,e,a){return t=t!==null?t.childLanes&~a:0,e&&(t|=Ke),t}function Bf(t,e,a){var l=e.pendingProps,n=!1,u=(e.flags&128)!==0,c;if((c=u)||(c=t!==null&&t.memoizedState===null?!1:($t.current&2)!==0),c&&(n=!0,e.flags&=-129),c=(e.flags&32)!==0,e.flags&=-33,t===null){if(xt){if(n?Ba(e):qa(),xt){var f=ne,d;if(d=f){t:{for(d=f,f=sa;d.nodeType!==8;){if(!f){f=null;break t}if(d=aa(d.nextSibling),d===null){f=null;break t}}f=d}f!==null?(e.memoizedState={dehydrated:f,treeContext:hl!==null?{id:pa,overflow:ya}:null,retryLane:536870912},d=Ze(18,null,null,0),d.stateNode=f,d.return=e,e.child=d,he=e,ne=null,d=!0):d=!1}d||ml(e)}if(f=e.memoizedState,f!==null&&(f=f.dehydrated,f!==null))return f.data==="$!"?e.lanes=16:e.lanes=536870912,null;ba(e)}return f=l.children,l=l.fallback,n?(qa(),n=e.mode,f=Wc({mode:"hidden",children:f},n),l=Tl(l,n,a,null),f.return=e,l.return=e,f.sibling=l,e.child=f,n=e.child,n.memoizedState=kc(a),n.childLanes=Jc(t,c,a),e.memoizedState=Kc,l):(Ba(e),$c(e,f))}if(d=t.memoizedState,d!==null&&(f=d.dehydrated,f!==null)){if(u)e.flags&256?(Ba(e),e.flags&=-257,e=Fc(t,e,a)):e.memoizedState!==null?(qa(),e.child=t.child,e.flags|=128,e=null):(qa(),n=l.fallback,f=e.mode,l=Wc({mode:"visible",children:l.children},f),n=Tl(n,f,a,null),n.flags|=2,l.return=e,n.return=e,l.sibling=n,e.child=l,vl(e,t.child,null,a),l=e.child,l.memoizedState=kc(a),l.childLanes=Jc(t,c,a),e.memoizedState=Kc,e=n);else if(Ba(e),f.data==="$!"){if(c=f.nextSibling&&f.nextSibling.dataset,c)var y=c.dgst;c=y,l=Error(r(419)),l.stack="",l.digest=c,qn({value:l,source:null,stack:null}),e=Fc(t,e,a)}else if(It||$n(t,e,a,!1),c=(a&t.childLanes)!==0,It||c){if(c=Rt,c!==null){if(l=a&-a,(l&42)!==0)l=1;else switch(l){case 2:l=1;break;case 8:l=4;break;case 32:l=16;break;case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:l=64;break;case 268435456:l=134217728;break;default:l=0}if(l=(l&(c.suspendedLanes|a))!==0?0:l,l!==0&&l!==d.retryLane)throw d.retryLane=l,Ha(t,l),ge(c,t,l),wf}f.data==="$?"||Ts(),e=Fc(t,e,a)}else f.data==="$?"?(e.flags|=128,e.child=t.child,e=Cm.bind(null,t),f._reactRetry=e,e=null):(t=d.treeContext,ne=aa(f.nextSibling),he=e,xt=!0,ta=null,sa=!1,t!==null&&(Ge[Le++]=pa,Ge[Le++]=ya,Ge[Le++]=hl,pa=t.id,ya=t.overflow,hl=e),e=$c(e,l.children),e.flags|=4096);return e}return n?(qa(),n=l.fallback,f=e.mode,d=t.child,y=d.sibling,l=Ja(d,{mode:"hidden",children:l.children}),l.subtreeFlags=d.subtreeFlags&31457280,y!==null?n=Ja(y,n):(n=Tl(n,f,a,null),n.flags|=2),n.return=e,l.return=e,l.sibling=n,e.child=l,l=n,n=e.child,f=t.child.memoizedState,f===null?f=kc(a):(d=f.cachePool,d!==null?(y=Wt._currentValue,d=d.parent!==y?{parent:y,pool:y}:d):d=Bo(),f={baseLanes:f.baseLanes|a,cachePool:d}),n.memoizedState=f,n.childLanes=Jc(t,c,a),e.memoizedState=Kc,l):(Ba(e),a=t.child,t=a.sibling,a=Ja(a,{mode:"visible",children:l.children}),a.return=e,a.sibling=null,t!==null&&(c=e.deletions,c===null?(e.deletions=[t],e.flags|=16):c.push(t)),e.child=a,e.memoizedState=null,a)}function $c(t,e){return e=Wc({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Wc(t,e){return rd(t,e,0,null)}function Fc(t,e,a){return vl(e,t.child,null,a),t=$c(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function qf(t,e,a){t.lanes|=e;var l=t.alternate;l!==null&&(l.lanes|=e),es(t.return,e,a)}function Pc(t,e,a,l,n){var u=t.memoizedState;u===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:l,tail:a,tailMode:n}:(u.isBackwards=e,u.rendering=null,u.renderingStartTime=0,u.last=l,u.tail=a,u.tailMode=n)}function Yf(t,e,a){var l=e.pendingProps,n=l.revealOrder,u=l.tail;if(ue(t,e,l.children,a),l=$t.current,(l&2)!==0)l=l&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&qf(t,a,e);else if(t.tag===19)qf(t,a,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}l&=1}switch(gt($t,l),n){case"forwards":for(a=e.child,n=null;a!==null;)t=a.alternate,t!==null&&Pu(t)===null&&(n=a),a=a.sibling;a=n,a===null?(n=e.child,e.child=null):(n=a.sibling,a.sibling=null),Pc(e,!1,n,a,u);break;case"backwards":for(a=null,n=e.child,e.child=null;n!==null;){if(t=n.alternate,t!==null&&Pu(t)===null){e.child=n;break}t=n.sibling,n.sibling=a,a=n,n=t}Pc(e,!0,a,null,u);break;case"together":Pc(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function Sa(t,e,a){if(t!==null&&(e.dependencies=t.dependencies),$a|=e.lanes,(a&e.childLanes)===0)if(t!==null){if($n(t,e,a,!1),(a&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(r(153));if(e.child!==null){for(t=e.child,a=Ja(t,t.pendingProps),e.child=a,a.return=e;t.sibling!==null;)t=t.sibling,a=a.sibling=Ja(t,t.pendingProps),a.return=e;a.sibling=null}return e.child}function Ic(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&oi(t)))}function bm(t,e,a){switch(e.tag){case 3:ha(e,e.stateNode.containerInfo),La(e,Wt,t.memoizedState.cache),Bn();break;case 27:case 5:_l(e);break;case 4:ha(e,e.stateNode.containerInfo);break;case 10:La(e,e.type,e.memoizedProps.value);break;case 13:var l=e.memoizedState;if(l!==null)return l.dehydrated!==null?(Ba(e),e.flags|=128,null):(a&e.child.childLanes)!==0?Bf(t,e,a):(Ba(e),t=Sa(t,e,a),t!==null?t.sibling:null);Ba(e);break;case 19:var n=(t.flags&128)!==0;if(l=(a&e.childLanes)!==0,l||($n(t,e,a,!1),l=(a&e.childLanes)!==0),n){if(l)return Yf(t,e,a);e.flags|=128}if(n=e.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),gt($t,$t.current),l)break;return null;case 22:case 23:return e.lanes=0,_f(t,e,a);case 24:La(e,Wt,t.memoizedState.cache)}return Sa(t,e,a)}function Gf(t,e,a){if(t!==null)if(t.memoizedProps!==e.pendingProps)It=!0;else{if(!Ic(t,a)&&(e.flags&128)===0)return It=!1,bm(t,e,a);It=(t.flags&131072)!==0}else It=!1,xt&&(e.flags&1048576)!==0&&Eo(e,ku,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var l=e.elementType,n=l._init;if(l=n(l._payload),e.type=l,typeof l=="function")hs(l)?(t=Sl(l,t),e.tag=1,e=jf(null,e,l,t,a)):(e.tag=0,e=Zc(null,e,l,t,a));else{if(l!=null){if(n=l.$$typeof,n===G){e.tag=11,e=Rf(null,e,l,t,a);break t}else if(n===ut){e.tag=14,e=Of(null,e,l,t,a);break t}}throw e=st(l)||l,Error(r(306,e,""))}}return e;case 0:return Zc(t,e,e.type,e.pendingProps,a);case 1:return l=e.type,n=Sl(l,e.pendingProps),jf(t,e,l,n,a);case 3:t:{if(ha(e,e.stateNode.containerInfo),t===null)throw Error(r(387));var u=e.pendingProps;n=e.memoizedState,l=n.element,ns(t,e),Pn(e,u,null,a);var c=e.memoizedState;if(u=c.cache,La(e,Wt,u),u!==n.cache&&as(e,[Wt],a,!0),Fn(),u=c.element,n.isDehydrated)if(n={element:u,isDehydrated:!1,cache:c.cache},e.updateQueue.baseState=n,e.memoizedState=n,e.flags&256){e=Hf(t,e,u,a);break t}else if(u!==l){l=Ye(Error(r(424)),e),qn(l),e=Hf(t,e,u,a);break t}else for(ne=aa(e.stateNode.containerInfo.firstChild),he=e,xt=!0,ta=null,sa=!0,a=_o(e,null,u,a),e.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling;else{if(Bn(),u===l){e=Sa(t,e,a);break t}ue(t,e,u,a)}e=e.child}return e;case 26:return Jn(t,e),t===null?(a=Qd(e.type,null,e.pendingProps,null))?e.memoizedState=a:xt||(a=e.type,t=e.pendingProps,l=Di(We.current).createElement(a),l[Vt]=e,l[be]=t,ie(l,a,t),Pt(l),e.stateNode=l):e.memoizedState=Qd(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return _l(e),t===null&&xt&&(l=e.stateNode=Gd(e.type,e.pendingProps,We.current),he=e,sa=!0,ne=aa(l.firstChild)),l=e.pendingProps.children,t!==null||xt?ue(t,e,l,a):e.child=vl(e,null,l,a),Jn(t,e),e.child;case 5:return t===null&&xt&&((n=l=ne)&&(l=$m(l,e.type,e.pendingProps,sa),l!==null?(e.stateNode=l,he=e,ne=aa(l.firstChild),sa=!1,n=!0):n=!1),n||ml(e)),_l(e),n=e.type,u=e.pendingProps,c=t!==null?t.memoizedProps:null,l=u.children,Ys(n,u)?l=null:c!==null&&Ys(n,c)&&(e.flags|=32),e.memoizedState!==null&&(n=zc(t,e,dm,null,null,a),hu._currentValue=n),Jn(t,e),ue(t,e,l,a),e.child;case 6:return t===null&&xt&&((t=a=ne)&&(a=Wm(a,e.pendingProps,sa),a!==null?(e.stateNode=a,he=e,ne=null,t=!0):t=!1),t||ml(e)),null;case 13:return Bf(t,e,a);case 4:return ha(e,e.stateNode.containerInfo),l=e.pendingProps,t===null?e.child=vl(e,null,l,a):ue(t,e,l,a),e.child;case 11:return Rf(t,e,e.type,e.pendingProps,a);case 7:return ue(t,e,e.pendingProps,a),e.child;case 8:return ue(t,e,e.pendingProps.children,a),e.child;case 12:return ue(t,e,e.pendingProps.children,a),e.child;case 10:return l=e.pendingProps,La(e,e.type,l.value),ue(t,e,l.children,a),e.child;case 9:return n=e.type._context,l=e.pendingProps.children,El(e),n=se(n),l=l(n),e.flags|=1,ue(t,e,l,a),e.child;case 14:return Of(t,e,e.type,e.pendingProps,a);case 15:return Nf(t,e,e.type,e.pendingProps,a);case 19:return Yf(t,e,a);case 22:return _f(t,e,a);case 24:return El(e),l=se(Wt),t===null?(n=Tc(),n===null&&(n=Rt,u=Ec(),n.pooledCache=u,u.refCount++,u!==null&&(n.pooledCacheLanes|=a),n=u),e.memoizedState={parent:l,cache:n},ls(e),La(e,Wt,n)):((t.lanes&a)!==0&&(ns(t,e),Pn(e,null,null,a),Fn()),n=t.memoizedState,u=e.memoizedState,n.parent!==l?(n={parent:l,cache:l},e.memoizedState=n,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=n),La(e,Wt,l)):(l=u.cache,La(e,Wt,l),l!==n.cache&&as(e,[Wt],a,!0))),ue(t,e,e.pendingProps.children,a),e.child;case 29:throw e.pendingProps}throw Error(r(156,e.tag))}var ts=bt(null),Al=null,Aa=null;function La(t,e,a){gt(ts,e._currentValue),e._currentValue=a}function Ea(t){t._currentValue=ts.current,Ut(ts)}function es(t,e,a){for(;t!==null;){var l=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,l!==null&&(l.childLanes|=e)):l!==null&&(l.childLanes&e)!==e&&(l.childLanes|=e),t===a)break;t=t.return}}function as(t,e,a,l){var n=t.child;for(n!==null&&(n.return=t);n!==null;){var u=n.dependencies;if(u!==null){var c=n.child;u=u.firstContext;t:for(;u!==null;){var f=u;u=n;for(var d=0;d<e.length;d++)if(f.context===e[d]){u.lanes|=a,f=u.alternate,f!==null&&(f.lanes|=a),es(u.return,a,t),l||(c=null);break t}u=f.next}}else if(n.tag===18){if(c=n.return,c===null)throw Error(r(341));c.lanes|=a,u=c.alternate,u!==null&&(u.lanes|=a),es(c,a,t),c=null}else c=n.child;if(c!==null)c.return=n;else for(c=n;c!==null;){if(c===t){c=null;break}if(n=c.sibling,n!==null){n.return=c.return,c=n;break}c=c.return}n=c}}function $n(t,e,a,l){t=null;for(var n=e,u=!1;n!==null;){if(!u){if((n.flags&524288)!==0)u=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var c=n.alternate;if(c===null)throw Error(r(387));if(c=c.memoizedProps,c!==null){var f=n.type;we(n.pendingProps.value,c.value)||(t!==null?t.push(f):t=[f])}}else if(n===Ce.current){if(c=n.alternate,c===null)throw Error(r(387));c.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(t!==null?t.push(hu):t=[hu])}n=n.return}t!==null&&as(e,t,a,l),e.flags|=262144}function oi(t){for(t=t.firstContext;t!==null;){if(!we(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function El(t){Al=t,Aa=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function se(t){return Lf(Al,t)}function fi(t,e){return Al===null&&El(t),Lf(t,e)}function Lf(t,e){var a=e._currentValue;if(e={context:e,memoizedValue:a,next:null},Aa===null){if(t===null)throw Error(r(308));Aa=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else Aa=Aa.next=e;return a}var Xa=!1;function ls(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function ns(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function Qa(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function Va(t,e,a){var l=t.updateQueue;if(l===null)return null;if(l=l.shared,(Bt&2)!==0){var n=l.pending;return n===null?e.next=e:(e.next=n.next,n.next=e),l.pending=e,e=Zu(t),So(t,null,a),e}return Vu(t,l,e,a),Zu(t)}function Wn(t,e,a){if(e=e.updateQueue,e!==null&&(e=e.shared,(a&4194176)!==0)){var l=e.lanes;l&=t.pendingLanes,a|=l,e.lanes=a,je(t,a)}}function us(t,e){var a=t.updateQueue,l=t.alternate;if(l!==null&&(l=l.updateQueue,a===l)){var n=null,u=null;if(a=a.firstBaseUpdate,a!==null){do{var c={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};u===null?n=u=c:u=u.next=c,a=a.next}while(a!==null);u===null?n=u=e:u=u.next=e}else n=u=e;a={baseState:l.baseState,firstBaseUpdate:n,lastBaseUpdate:u,shared:l.shared,callbacks:l.callbacks},t.updateQueue=a;return}t=a.lastBaseUpdate,t===null?a.firstBaseUpdate=e:t.next=e,a.lastBaseUpdate=e}var is=!1;function Fn(){if(is){var t=Il;if(t!==null)throw t}}function Pn(t,e,a,l){is=!1;var n=t.updateQueue;Xa=!1;var u=n.firstBaseUpdate,c=n.lastBaseUpdate,f=n.shared.pending;if(f!==null){n.shared.pending=null;var d=f,y=d.next;d.next=null,c===null?u=y:c.next=y,c=d;var D=t.alternate;D!==null&&(D=D.updateQueue,f=D.lastBaseUpdate,f!==c&&(f===null?D.firstBaseUpdate=y:f.next=y,D.lastBaseUpdate=d))}if(u!==null){var C=n.baseState;c=0,D=y=d=null,f=u;do{var E=f.lane&-536870913,T=E!==f.lane;if(T?(yt&E)===E:(l&E)===E){E!==0&&E===Pl&&(is=!0),D!==null&&(D=D.next={lane:0,tag:f.tag,payload:f.payload,callback:null,next:null});t:{var J=t,it=f;E=e;var Gt=a;switch(it.tag){case 1:if(J=it.payload,typeof J=="function"){C=J.call(Gt,C,E);break t}C=J;break t;case 3:J.flags=J.flags&-65537|128;case 0:if(J=it.payload,E=typeof J=="function"?J.call(Gt,C,E):J,E==null)break t;C=$({},C,E);break t;case 2:Xa=!0}}E=f.callback,E!==null&&(t.flags|=64,T&&(t.flags|=8192),T=n.callbacks,T===null?n.callbacks=[E]:T.push(E))}else T={lane:E,tag:f.tag,payload:f.payload,callback:f.callback,next:null},D===null?(y=D=T,d=C):D=D.next=T,c|=E;if(f=f.next,f===null){if(f=n.shared.pending,f===null)break;T=f,f=T.next,T.next=null,n.lastBaseUpdate=T,n.shared.pending=null}}while(!0);D===null&&(d=C),n.baseState=d,n.firstBaseUpdate=y,n.lastBaseUpdate=D,u===null&&(n.shared.lanes=0),$a|=c,t.lanes=c,t.memoizedState=C}}function Xf(t,e){if(typeof t!="function")throw Error(r(191,t));t.call(e)}function Qf(t,e){var a=t.callbacks;if(a!==null)for(t.callbacks=null,t=0;t<a.length;t++)Xf(a[t],e)}function In(t,e){try{var a=e.updateQueue,l=a!==null?a.lastEffect:null;if(l!==null){var n=l.next;a=n;do{if((a.tag&t)===t){l=void 0;var u=a.create,c=a.inst;l=u(),c.destroy=l}a=a.next}while(a!==n)}}catch(f){wt(e,e.return,f)}}function Za(t,e,a){try{var l=e.updateQueue,n=l!==null?l.lastEffect:null;if(n!==null){var u=n.next;l=u;do{if((l.tag&t)===t){var c=l.inst,f=c.destroy;if(f!==void 0){c.destroy=void 0,n=e;var d=a;try{f()}catch(y){wt(n,d,y)}}}l=l.next}while(l!==u)}}catch(y){wt(e,e.return,y)}}function Vf(t){var e=t.updateQueue;if(e!==null){var a=t.stateNode;try{Qf(e,a)}catch(l){wt(t,t.return,l)}}}function Zf(t,e,a){a.props=Sl(t.type,t.memoizedProps),a.state=t.memoizedState;try{a.componentWillUnmount()}catch(l){wt(t,e,l)}}function Ml(t,e){try{var a=t.ref;if(a!==null){var l=t.stateNode;switch(t.tag){case 26:case 27:case 5:var n=l;break;default:n=l}typeof a=="function"?t.refCleanup=a(n):a.current=n}}catch(u){wt(t,e,u)}}function Re(t,e){var a=t.ref,l=t.refCleanup;if(a!==null)if(typeof l=="function")try{l()}catch(n){wt(t,e,n)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(n){wt(t,e,n)}else a.current=null}function Kf(t){var e=t.type,a=t.memoizedProps,l=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":a.autoFocus&&l.focus();break t;case"img":a.src?l.src=a.src:a.srcSet&&(l.srcset=a.srcSet)}}catch(n){wt(t,t.return,n)}}function kf(t,e,a){try{var l=t.stateNode;Vm(l,t.type,a,e),l[be]=e}catch(n){wt(t,t.return,n)}}function Jf(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27||t.tag===4}function cs(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Jf(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==27&&t.tag!==18;){if(t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function ss(t,e,a){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?a.nodeType===8?a.parentNode.insertBefore(t,e):a.insertBefore(t,e):(a.nodeType===8?(e=a.parentNode,e.insertBefore(t,a)):(e=a,e.appendChild(t)),a=a._reactRootContainer,a!=null||e.onclick!==null||(e.onclick=Ti));else if(l!==4&&l!==27&&(t=t.child,t!==null))for(ss(t,e,a),t=t.sibling;t!==null;)ss(t,e,a),t=t.sibling}function di(t,e,a){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?a.insertBefore(t,e):a.appendChild(t);else if(l!==4&&l!==27&&(t=t.child,t!==null))for(di(t,e,a),t=t.sibling;t!==null;)di(t,e,a),t=t.sibling}var Ma=!1,qt=!1,rs=!1,$f=typeof WeakSet=="function"?WeakSet:Set,te=null,Wf=!1;function xm(t,e){if(t=t.containerInfo,Bs=_i,t=fo(t),fc(t)){if("selectionStart"in t)var a={start:t.selectionStart,end:t.selectionEnd};else t:{a=(a=t.ownerDocument)&&a.defaultView||window;var l=a.getSelection&&a.getSelection();if(l&&l.rangeCount!==0){a=l.anchorNode;var n=l.anchorOffset,u=l.focusNode;l=l.focusOffset;try{a.nodeType,u.nodeType}catch{a=null;break t}var c=0,f=-1,d=-1,y=0,D=0,C=t,E=null;e:for(;;){for(var T;C!==a||n!==0&&C.nodeType!==3||(f=c+n),C!==u||l!==0&&C.nodeType!==3||(d=c+l),C.nodeType===3&&(c+=C.nodeValue.length),(T=C.firstChild)!==null;)E=C,C=T;for(;;){if(C===t)break e;if(E===a&&++y===n&&(f=c),E===u&&++D===l&&(d=c),(T=C.nextSibling)!==null)break;C=E,E=C.parentNode}C=T}a=f===-1||d===-1?null:{start:f,end:d}}else a=null}a=a||{start:0,end:0}}else a=null;for(qs={focusedElem:t,selectionRange:a},_i=!1,te=e;te!==null;)if(e=te,t=e.child,(e.subtreeFlags&1028)!==0&&t!==null)t.return=e,te=t;else for(;te!==null;){switch(e=te,u=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&u!==null){t=void 0,a=e,n=u.memoizedProps,u=u.memoizedState,l=a.stateNode;try{var J=Sl(a.type,n,a.elementType===a.type);t=l.getSnapshotBeforeUpdate(J,u),l.__reactInternalSnapshotBeforeUpdate=t}catch(it){wt(a,a.return,it)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,a=t.nodeType,a===9)Xs(t);else if(a===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":Xs(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(r(163))}if(t=e.sibling,t!==null){t.return=e.return,te=t;break}te=e.return}return J=Wf,Wf=!1,J}function Ff(t,e,a){var l=a.flags;switch(a.tag){case 0:case 11:case 15:Da(t,a),l&4&&In(5,a);break;case 1:if(Da(t,a),l&4)if(t=a.stateNode,e===null)try{t.componentDidMount()}catch(f){wt(a,a.return,f)}else{var n=Sl(a.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(n,e,t.__reactInternalSnapshotBeforeUpdate)}catch(f){wt(a,a.return,f)}}l&64&&Vf(a),l&512&&Ml(a,a.return);break;case 3:if(Da(t,a),l&64&&(l=a.updateQueue,l!==null)){if(t=null,a.child!==null)switch(a.child.tag){case 27:case 5:t=a.child.stateNode;break;case 1:t=a.child.stateNode}try{Qf(l,t)}catch(f){wt(a,a.return,f)}}break;case 26:Da(t,a),l&512&&Ml(a,a.return);break;case 27:case 5:Da(t,a),e===null&&l&4&&Kf(a),l&512&&Ml(a,a.return);break;case 12:Da(t,a);break;case 13:Da(t,a),l&4&&td(t,a);break;case 22:if(n=a.memoizedState!==null||Ma,!n){e=e!==null&&e.memoizedState!==null||qt;var u=Ma,c=qt;Ma=n,(qt=e)&&!c?Ka(t,a,(a.subtreeFlags&8772)!==0):Da(t,a),Ma=u,qt=c}l&512&&(a.memoizedProps.mode==="manual"?Ml(a,a.return):Re(a,a.return));break;default:Da(t,a)}}function Pf(t){var e=t.alternate;e!==null&&(t.alternate=null,Pf(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&$i(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var kt=null,Oe=!1;function Ta(t,e,a){for(a=a.child;a!==null;)If(t,e,a),a=a.sibling}function If(t,e,a){if(fe&&typeof fe.onCommitFiberUnmount=="function")try{fe.onCommitFiberUnmount(cl,a)}catch{}switch(a.tag){case 26:qt||Re(a,e),Ta(t,e,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:qt||Re(a,e);var l=kt,n=Oe;for(kt=a.stateNode,Ta(t,e,a),a=a.stateNode,e=a.attributes;e.length;)a.removeAttributeNode(e[0]);$i(a),kt=l,Oe=n;break;case 5:qt||Re(a,e);case 6:n=kt;var u=Oe;if(kt=null,Ta(t,e,a),kt=n,Oe=u,kt!==null)if(Oe)try{t=kt,l=a.stateNode,t.nodeType===8?t.parentNode.removeChild(l):t.removeChild(l)}catch(c){wt(a,e,c)}else try{kt.removeChild(a.stateNode)}catch(c){wt(a,e,c)}break;case 18:kt!==null&&(Oe?(e=kt,a=a.stateNode,e.nodeType===8?Ls(e.parentNode,a):e.nodeType===1&&Ls(e,a),pu(e)):Ls(kt,a.stateNode));break;case 4:l=kt,n=Oe,kt=a.stateNode.containerInfo,Oe=!0,Ta(t,e,a),kt=l,Oe=n;break;case 0:case 11:case 14:case 15:qt||Za(2,a,e),qt||Za(4,a,e),Ta(t,e,a);break;case 1:qt||(Re(a,e),l=a.stateNode,typeof l.componentWillUnmount=="function"&&Zf(a,e,l)),Ta(t,e,a);break;case 21:Ta(t,e,a);break;case 22:qt||Re(a,e),qt=(l=qt)||a.memoizedState!==null,Ta(t,e,a),qt=l;break;default:Ta(t,e,a)}}function td(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{pu(t)}catch(a){wt(e,e.return,a)}}function Sm(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new $f),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new $f),e;default:throw Error(r(435,t.tag))}}function os(t,e){var a=Sm(t);e.forEach(function(l){var n=Um.bind(null,t,l);a.has(l)||(a.add(l),l.then(n,n))})}function Qe(t,e){var a=e.deletions;if(a!==null)for(var l=0;l<a.length;l++){var n=a[l],u=t,c=e,f=c;t:for(;f!==null;){switch(f.tag){case 27:case 5:kt=f.stateNode,Oe=!1;break t;case 3:kt=f.stateNode.containerInfo,Oe=!0;break t;case 4:kt=f.stateNode.containerInfo,Oe=!0;break t}f=f.return}if(kt===null)throw Error(r(160));If(u,c,n),kt=null,Oe=!1,u=n.alternate,u!==null&&(u.return=null),n.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)ed(e,t),e=e.sibling}var ea=null;function ed(t,e){var a=t.alternate,l=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:Qe(e,t),Ve(t),l&4&&(Za(3,t,t.return),In(3,t),Za(5,t,t.return));break;case 1:Qe(e,t),Ve(t),l&512&&(qt||a===null||Re(a,a.return)),l&64&&Ma&&(t=t.updateQueue,t!==null&&(l=t.callbacks,l!==null&&(a=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=a===null?l:a.concat(l))));break;case 26:var n=ea;if(Qe(e,t),Ve(t),l&512&&(qt||a===null||Re(a,a.return)),l&4){var u=a!==null?a.memoizedState:null;if(l=t.memoizedState,a===null)if(l===null)if(t.stateNode===null){t:{l=t.type,a=t.memoizedProps,n=n.ownerDocument||n;e:switch(l){case"title":u=n.getElementsByTagName("title")[0],(!u||u[Tn]||u[Vt]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=n.createElement(l),n.head.insertBefore(u,n.querySelector("head > title"))),ie(u,l,a),u[Vt]=t,Pt(u),l=u;break t;case"link":var c=Kd("link","href",n).get(l+(a.href||""));if(c){for(var f=0;f<c.length;f++)if(u=c[f],u.getAttribute("href")===(a.href==null?null:a.href)&&u.getAttribute("rel")===(a.rel==null?null:a.rel)&&u.getAttribute("title")===(a.title==null?null:a.title)&&u.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){c.splice(f,1);break e}}u=n.createElement(l),ie(u,l,a),n.head.appendChild(u);break;case"meta":if(c=Kd("meta","content",n).get(l+(a.content||""))){for(f=0;f<c.length;f++)if(u=c[f],u.getAttribute("content")===(a.content==null?null:""+a.content)&&u.getAttribute("name")===(a.name==null?null:a.name)&&u.getAttribute("property")===(a.property==null?null:a.property)&&u.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&u.getAttribute("charset")===(a.charSet==null?null:a.charSet)){c.splice(f,1);break e}}u=n.createElement(l),ie(u,l,a),n.head.appendChild(u);break;default:throw Error(r(468,l))}u[Vt]=t,Pt(u),l=u}t.stateNode=l}else kd(n,t.type,t.stateNode);else t.stateNode=Zd(n,l,t.memoizedProps);else u!==l?(u===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):u.count--,l===null?kd(n,t.type,t.stateNode):Zd(n,l,t.memoizedProps)):l===null&&t.stateNode!==null&&kf(t,t.memoizedProps,a.memoizedProps)}break;case 27:if(l&4&&t.alternate===null){n=t.stateNode,u=t.memoizedProps;try{for(var d=n.firstChild;d;){var y=d.nextSibling,D=d.nodeName;d[Tn]||D==="HEAD"||D==="BODY"||D==="SCRIPT"||D==="STYLE"||D==="LINK"&&d.rel.toLowerCase()==="stylesheet"||n.removeChild(d),d=y}for(var C=t.type,E=n.attributes;E.length;)n.removeAttributeNode(E[0]);ie(n,C,u),n[Vt]=t,n[be]=u}catch(J){wt(t,t.return,J)}}case 5:if(Qe(e,t),Ve(t),l&512&&(qt||a===null||Re(a,a.return)),t.flags&32){n=t.stateNode;try{Gl(n,"")}catch(J){wt(t,t.return,J)}}l&4&&t.stateNode!=null&&(n=t.memoizedProps,kf(t,n,a!==null?a.memoizedProps:n)),l&1024&&(rs=!0);break;case 6:if(Qe(e,t),Ve(t),l&4){if(t.stateNode===null)throw Error(r(162));l=t.memoizedProps,a=t.stateNode;try{a.nodeValue=l}catch(J){wt(t,t.return,J)}}break;case 3:if(Ri=null,n=ea,ea=zi(e.containerInfo),Qe(e,t),ea=n,Ve(t),l&4&&a!==null&&a.memoizedState.isDehydrated)try{pu(e.containerInfo)}catch(J){wt(t,t.return,J)}rs&&(rs=!1,ad(t));break;case 4:l=ea,ea=zi(t.stateNode.containerInfo),Qe(e,t),Ve(t),ea=l;break;case 12:Qe(e,t),Ve(t);break;case 13:Qe(e,t),Ve(t),t.child.flags&8192&&t.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(bs=Te()),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,os(t,l)));break;case 22:if(l&512&&(qt||a===null||Re(a,a.return)),d=t.memoizedState!==null,y=a!==null&&a.memoizedState!==null,D=Ma,C=qt,Ma=D||d,qt=C||y,Qe(e,t),qt=C,Ma=D,Ve(t),e=t.stateNode,e._current=t,e._visibility&=-3,e._visibility|=e._pendingVisibility&2,l&8192&&(e._visibility=d?e._visibility&-2:e._visibility|1,d&&(e=Ma||qt,a===null||y||e||ln(t)),t.memoizedProps===null||t.memoizedProps.mode!=="manual"))t:for(a=null,e=t;;){if(e.tag===5||e.tag===26||e.tag===27){if(a===null){y=a=e;try{if(n=y.stateNode,d)u=n.style,typeof u.setProperty=="function"?u.setProperty("display","none","important"):u.display="none";else{c=y.stateNode,f=y.memoizedProps.style;var T=f!=null&&f.hasOwnProperty("display")?f.display:null;c.style.display=T==null||typeof T=="boolean"?"":(""+T).trim()}}catch(J){wt(y,y.return,J)}}}else if(e.tag===6){if(a===null){y=e;try{y.stateNode.nodeValue=d?"":y.memoizedProps}catch(J){wt(y,y.return,J)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;a===e&&(a=null),e=e.return}a===e&&(a=null),e.sibling.return=e.return,e=e.sibling}l&4&&(l=t.updateQueue,l!==null&&(a=l.retryQueue,a!==null&&(l.retryQueue=null,os(t,a))));break;case 19:Qe(e,t),Ve(t),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,os(t,l)));break;case 21:break;default:Qe(e,t),Ve(t)}}function Ve(t){var e=t.flags;if(e&2){try{if(t.tag!==27){t:{for(var a=t.return;a!==null;){if(Jf(a)){var l=a;break t}a=a.return}throw Error(r(160))}switch(l.tag){case 27:var n=l.stateNode,u=cs(t);di(t,u,n);break;case 5:var c=l.stateNode;l.flags&32&&(Gl(c,""),l.flags&=-33);var f=cs(t);di(t,f,c);break;case 3:case 4:var d=l.stateNode.containerInfo,y=cs(t);ss(t,y,d);break;default:throw Error(r(161))}}}catch(D){wt(t,t.return,D)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function ad(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;ad(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function Da(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Ff(t,e.alternate,e),e=e.sibling}function ln(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:Za(4,e,e.return),ln(e);break;case 1:Re(e,e.return);var a=e.stateNode;typeof a.componentWillUnmount=="function"&&Zf(e,e.return,a),ln(e);break;case 26:case 27:case 5:Re(e,e.return),ln(e);break;case 22:Re(e,e.return),e.memoizedState===null&&ln(e);break;default:ln(e)}t=t.sibling}}function Ka(t,e,a){for(a=a&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var l=e.alternate,n=t,u=e,c=u.flags;switch(u.tag){case 0:case 11:case 15:Ka(n,u,a),In(4,u);break;case 1:if(Ka(n,u,a),l=u,n=l.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(y){wt(l,l.return,y)}if(l=u,n=l.updateQueue,n!==null){var f=l.stateNode;try{var d=n.shared.hiddenCallbacks;if(d!==null)for(n.shared.hiddenCallbacks=null,n=0;n<d.length;n++)Xf(d[n],f)}catch(y){wt(l,l.return,y)}}a&&c&64&&Vf(u),Ml(u,u.return);break;case 26:case 27:case 5:Ka(n,u,a),a&&l===null&&c&4&&Kf(u),Ml(u,u.return);break;case 12:Ka(n,u,a);break;case 13:Ka(n,u,a),a&&c&4&&td(n,u);break;case 22:u.memoizedState===null&&Ka(n,u,a),Ml(u,u.return);break;default:Ka(n,u,a)}e=e.sibling}}function fs(t,e){var a=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==a&&(t!=null&&t.refCount++,a!=null&&Qn(a))}function ds(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Qn(t))}function ka(t,e,a,l){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)ld(t,e,a,l),e=e.sibling}function ld(t,e,a,l){var n=e.flags;switch(e.tag){case 0:case 11:case 15:ka(t,e,a,l),n&2048&&In(9,e);break;case 3:ka(t,e,a,l),n&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Qn(t)));break;case 12:if(n&2048){ka(t,e,a,l),t=e.stateNode;try{var u=e.memoizedProps,c=u.id,f=u.onPostCommit;typeof f=="function"&&f(c,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(d){wt(e,e.return,d)}}else ka(t,e,a,l);break;case 23:break;case 22:u=e.stateNode,e.memoizedState!==null?u._visibility&4?ka(t,e,a,l):tu(t,e):u._visibility&4?ka(t,e,a,l):(u._visibility|=4,nn(t,e,a,l,(e.subtreeFlags&10256)!==0)),n&2048&&fs(e.alternate,e);break;case 24:ka(t,e,a,l),n&2048&&ds(e.alternate,e);break;default:ka(t,e,a,l)}}function nn(t,e,a,l,n){for(n=n&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var u=t,c=e,f=a,d=l,y=c.flags;switch(c.tag){case 0:case 11:case 15:nn(u,c,f,d,n),In(8,c);break;case 23:break;case 22:var D=c.stateNode;c.memoizedState!==null?D._visibility&4?nn(u,c,f,d,n):tu(u,c):(D._visibility|=4,nn(u,c,f,d,n)),n&&y&2048&&fs(c.alternate,c);break;case 24:nn(u,c,f,d,n),n&&y&2048&&ds(c.alternate,c);break;default:nn(u,c,f,d,n)}e=e.sibling}}function tu(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var a=t,l=e,n=l.flags;switch(l.tag){case 22:tu(a,l),n&2048&&fs(l.alternate,l);break;case 24:tu(a,l),n&2048&&ds(l.alternate,l);break;default:tu(a,l)}e=e.sibling}}var eu=8192;function un(t){if(t.subtreeFlags&eu)for(t=t.child;t!==null;)nd(t),t=t.sibling}function nd(t){switch(t.tag){case 26:un(t),t.flags&eu&&t.memoizedState!==null&&r0(ea,t.memoizedState,t.memoizedProps);break;case 5:un(t);break;case 3:case 4:var e=ea;ea=zi(t.stateNode.containerInfo),un(t),ea=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=eu,eu=16777216,un(t),eu=e):un(t));break;default:un(t)}}function ud(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function au(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var a=0;a<e.length;a++){var l=e[a];te=l,cd(l,t)}ud(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)id(t),t=t.sibling}function id(t){switch(t.tag){case 0:case 11:case 15:au(t),t.flags&2048&&Za(9,t,t.return);break;case 3:au(t);break;case 12:au(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&4&&(t.return===null||t.return.tag!==13)?(e._visibility&=-5,hi(t)):au(t);break;default:au(t)}}function hi(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var a=0;a<e.length;a++){var l=e[a];te=l,cd(l,t)}ud(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:Za(8,e,e.return),hi(e);break;case 22:a=e.stateNode,a._visibility&4&&(a._visibility&=-5,hi(e));break;default:hi(e)}t=t.sibling}}function cd(t,e){for(;te!==null;){var a=te;switch(a.tag){case 0:case 11:case 15:Za(8,a,e);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var l=a.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:Qn(a.memoizedState.cache)}if(l=a.child,l!==null)l.return=a,te=l;else t:for(a=t;te!==null;){l=te;var n=l.sibling,u=l.return;if(Pf(l),l===a){te=null;break t}if(n!==null){n.return=u,te=n;break t}te=u}}}function Am(t,e,a,l){this.tag=t,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ze(t,e,a,l){return new Am(t,e,a,l)}function hs(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Ja(t,e){var a=t.alternate;return a===null?(a=Ze(t.tag,e,t.key,t.mode),a.elementType=t.elementType,a.type=t.type,a.stateNode=t.stateNode,a.alternate=t,t.alternate=a):(a.pendingProps=e,a.type=t.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=t.flags&31457280,a.childLanes=t.childLanes,a.lanes=t.lanes,a.child=t.child,a.memoizedProps=t.memoizedProps,a.memoizedState=t.memoizedState,a.updateQueue=t.updateQueue,e=t.dependencies,a.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},a.sibling=t.sibling,a.index=t.index,a.ref=t.ref,a.refCleanup=t.refCleanup,a}function sd(t,e){t.flags&=31457282;var a=t.alternate;return a===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=a.childLanes,t.lanes=a.lanes,t.child=a.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=a.memoizedProps,t.memoizedState=a.memoizedState,t.updateQueue=a.updateQueue,t.type=a.type,e=a.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function gi(t,e,a,l,n,u){var c=0;if(l=t,typeof t=="function")hs(t)&&(c=1);else if(typeof t=="string")c=c0(t,a,me.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case x:return Tl(a.children,n,u,e);case p:c=8,n|=24;break;case N:return t=Ze(12,a,e,n|2),t.elementType=N,t.lanes=u,t;case H:return t=Ze(13,a,e,n),t.elementType=H,t.lanes=u,t;case tt:return t=Ze(19,a,e,n),t.elementType=tt,t.lanes=u,t;case dt:return rd(a,n,u,e);default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case j:case X:c=10;break t;case O:c=9;break t;case G:c=11;break t;case ut:c=14;break t;case nt:c=16,l=null;break t}c=29,a=Error(r(130,t===null?"null":typeof t,"")),l=null}return e=Ze(c,a,e,n),e.elementType=t,e.type=l,e.lanes=u,e}function Tl(t,e,a,l){return t=Ze(7,t,l,e),t.lanes=a,t}function rd(t,e,a,l){t=Ze(22,t,l,e),t.elementType=dt,t.lanes=a;var n={_visibility:1,_pendingVisibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null,_current:null,detach:function(){var u=n._current;if(u===null)throw Error(r(456));if((n._pendingVisibility&2)===0){var c=Ha(u,2);c!==null&&(n._pendingVisibility|=2,ge(c,u,2))}},attach:function(){var u=n._current;if(u===null)throw Error(r(456));if((n._pendingVisibility&2)!==0){var c=Ha(u,2);c!==null&&(n._pendingVisibility&=-3,ge(c,u,2))}}};return t.stateNode=n,t}function gs(t,e,a){return t=Ze(6,t,null,e),t.lanes=a,t}function ms(t,e,a){return e=Ze(4,t.children!==null?t.children:[],t.key,e),e.lanes=a,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}function za(t){t.flags|=4}function od(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Jd(e)){if(e=Xe.current,e!==null&&((yt&4194176)===yt?ra!==null:(yt&62914560)!==yt&&(yt&536870912)===0||e!==ra))throw Gn=xc,Do;t.flags|=8192}}function mi(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?De():536870912,t.lanes|=e,sn|=e)}function lu(t,e){if(!xt)switch(t.tailMode){case"hidden":e=t.tail;for(var a=null;e!==null;)e.alternate!==null&&(a=e),e=e.sibling;a===null?t.tail=null:a.sibling=null;break;case"collapsed":a=t.tail;for(var l=null;a!==null;)a.alternate!==null&&(l=a),a=a.sibling;l===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:l.sibling=null}}function Ht(t){var e=t.alternate!==null&&t.alternate.child===t.child,a=0,l=0;if(e)for(var n=t.child;n!==null;)a|=n.lanes|n.childLanes,l|=n.subtreeFlags&31457280,l|=n.flags&31457280,n.return=t,n=n.sibling;else for(n=t.child;n!==null;)a|=n.lanes|n.childLanes,l|=n.subtreeFlags,l|=n.flags,n.return=t,n=n.sibling;return t.subtreeFlags|=l,t.childLanes=a,e}function Em(t,e,a){var l=e.pendingProps;switch(yc(e),e.tag){case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ht(e),null;case 1:return Ht(e),null;case 3:return a=e.stateNode,l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),Ea(Wt),Me(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(t===null||t.child===null)&&(Hn(e)?za(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,ta!==null&&(Es(ta),ta=null))),Ht(e),null;case 26:return a=e.memoizedState,t===null?(za(e),a!==null?(Ht(e),od(e,a)):(Ht(e),e.flags&=-16777217)):a?a!==t.memoizedState?(za(e),Ht(e),od(e,a)):(Ht(e),e.flags&=-16777217):(t.memoizedProps!==l&&za(e),Ht(e),e.flags&=-16777217),null;case 27:nl(e),a=We.current;var n=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==l&&za(e);else{if(!l){if(e.stateNode===null)throw Error(r(166));return Ht(e),null}t=me.current,Hn(e)?Mo(e):(t=Gd(n,l,a),e.stateNode=t,za(e))}return Ht(e),null;case 5:if(nl(e),a=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==l&&za(e);else{if(!l){if(e.stateNode===null)throw Error(r(166));return Ht(e),null}if(t=me.current,Hn(e))Mo(e);else{switch(n=Di(We.current),t){case 1:t=n.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:t=n.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":t=n.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":t=n.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":t=n.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof l.is=="string"?n.createElement("select",{is:l.is}):n.createElement("select"),l.multiple?t.multiple=!0:l.size&&(t.size=l.size);break;default:t=typeof l.is=="string"?n.createElement(a,{is:l.is}):n.createElement(a)}}t[Vt]=e,t[be]=l;t:for(n=e.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break t;for(;n.sibling===null;){if(n.return===null||n.return===e)break t;n=n.return}n.sibling.return=n.return,n=n.sibling}e.stateNode=t;t:switch(ie(t,a,l),a){case"button":case"input":case"select":case"textarea":t=!!l.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&za(e)}}return Ht(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==l&&za(e);else{if(typeof l!="string"&&e.stateNode===null)throw Error(r(166));if(t=We.current,Hn(e)){if(t=e.stateNode,a=e.memoizedProps,l=null,n=he,n!==null)switch(n.tag){case 27:case 5:l=n.memoizedProps}t[Vt]=e,t=!!(t.nodeValue===a||l!==null&&l.suppressHydrationWarning===!0||Ud(t.nodeValue,a)),t||ml(e)}else t=Di(t).createTextNode(l),t[Vt]=e,e.stateNode=t}return Ht(e),null;case 13:if(l=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(n=Hn(e),l!==null&&l.dehydrated!==null){if(t===null){if(!n)throw Error(r(318));if(n=e.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(r(317));n[Vt]=e}else Bn(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Ht(e),n=!1}else ta!==null&&(Es(ta),ta=null),n=!0;if(!n)return e.flags&256?(ba(e),e):(ba(e),null)}if(ba(e),(e.flags&128)!==0)return e.lanes=a,e;if(a=l!==null,t=t!==null&&t.memoizedState!==null,a){l=e.child,n=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(n=l.alternate.memoizedState.cachePool.pool);var u=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(u=l.memoizedState.cachePool.pool),u!==n&&(l.flags|=2048)}return a!==t&&a&&(e.child.flags|=8192),mi(e,e.updateQueue),Ht(e),null;case 4:return Me(),t===null&&Us(e.stateNode.containerInfo),Ht(e),null;case 10:return Ea(e.type),Ht(e),null;case 19:if(Ut($t),n=e.memoizedState,n===null)return Ht(e),null;if(l=(e.flags&128)!==0,u=n.rendering,u===null)if(l)lu(n,!1);else{if(Yt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(u=Pu(t),u!==null){for(e.flags|=128,lu(n,!1),t=u.updateQueue,e.updateQueue=t,mi(e,t),e.subtreeFlags=0,t=a,a=e.child;a!==null;)sd(a,t),a=a.sibling;return gt($t,$t.current&1|2),e.child}t=t.sibling}n.tail!==null&&Te()>vi&&(e.flags|=128,l=!0,lu(n,!1),e.lanes=4194304)}else{if(!l)if(t=Pu(u),t!==null){if(e.flags|=128,l=!0,t=t.updateQueue,e.updateQueue=t,mi(e,t),lu(n,!0),n.tail===null&&n.tailMode==="hidden"&&!u.alternate&&!xt)return Ht(e),null}else 2*Te()-n.renderingStartTime>vi&&a!==536870912&&(e.flags|=128,l=!0,lu(n,!1),e.lanes=4194304);n.isBackwards?(u.sibling=e.child,e.child=u):(t=n.last,t!==null?t.sibling=u:e.child=u,n.last=u)}return n.tail!==null?(e=n.tail,n.rendering=e,n.tail=e.sibling,n.renderingStartTime=Te(),e.sibling=null,t=$t.current,gt($t,l?t&1|2:t&1),e):(Ht(e),null);case 22:case 23:return ba(e),Ac(),l=e.memoizedState!==null,t!==null?t.memoizedState!==null!==l&&(e.flags|=8192):l&&(e.flags|=8192),l?(a&536870912)!==0&&(e.flags&128)===0&&(Ht(e),e.subtreeFlags&6&&(e.flags|=8192)):Ht(e),a=e.updateQueue,a!==null&&mi(e,a.retryQueue),a=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),l=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),l!==a&&(e.flags|=2048),t!==null&&Ut(pl),null;case 24:return a=null,t!==null&&(a=t.memoizedState.cache),e.memoizedState.cache!==a&&(e.flags|=2048),Ea(Wt),Ht(e),null;case 25:return null}throw Error(r(156,e.tag))}function Mm(t,e){switch(yc(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Ea(Wt),Me(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return nl(e),null;case 13:if(ba(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(r(340));Bn()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return Ut($t),null;case 4:return Me(),null;case 10:return Ea(e.type),null;case 22:case 23:return ba(e),Ac(),t!==null&&Ut(pl),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return Ea(Wt),null;case 25:return null;default:return null}}function fd(t,e){switch(yc(e),e.tag){case 3:Ea(Wt),Me();break;case 26:case 27:case 5:nl(e);break;case 4:Me();break;case 13:ba(e);break;case 19:Ut($t);break;case 10:Ea(e.type);break;case 22:case 23:ba(e),Ac(),t!==null&&Ut(pl);break;case 24:Ea(Wt)}}var Tm={getCacheForType:function(t){var e=se(Wt),a=e.data.get(t);return a===void 0&&(a=t(),e.data.set(t,a)),a}},Dm=typeof WeakMap=="function"?WeakMap:Map,Bt=0,Rt=null,ht=null,yt=0,Ot=0,Ne=null,wa=!1,cn=!1,vs=!1,Ra=0,Yt=0,$a=0,Dl=0,ps=0,Ke=0,sn=0,nu=null,fa=null,ys=!1,bs=0,vi=1/0,pi=null,Wa=null,yi=!1,zl=null,uu=0,xs=0,Ss=null,iu=0,As=null;function _e(){if((Bt&2)!==0&&yt!==0)return yt&-yt;if(Y.T!==null){var t=Pl;return t!==0?t:Os()}return Ua()}function dd(){Ke===0&&(Ke=(yt&536870912)===0||xt?ce():536870912);var t=Xe.current;return t!==null&&(t.flags|=32),Ke}function ge(t,e,a){(t===Rt&&Ot===2||t.cancelPendingCommit!==null)&&(rn(t,0),Oa(t,yt,Ke,!1)),jt(t,a),((Bt&2)===0||t!==Rt)&&(t===Rt&&((Bt&2)===0&&(Dl|=a),Yt===4&&Oa(t,yt,Ke,!1)),da(t))}function hd(t,e,a){if((Bt&6)!==0)throw Error(r(327));var l=!a&&(e&60)===0&&(e&t.expiredLanes)===0||ve(t,e),n=l?Rm(t,e):Ds(t,e,!0),u=l;do{if(n===0){cn&&!l&&Oa(t,e,0,!1);break}else if(n===6)Oa(t,e,0,!wa);else{if(a=t.current.alternate,u&&!zm(a)){n=Ds(t,e,!1),u=!1;continue}if(n===2){if(u=e,t.errorRecoveryDisabledLanes&u)var c=0;else c=t.pendingLanes&-536870913,c=c!==0?c:c&536870912?536870912:0;if(c!==0){e=c;t:{var f=t;n=nu;var d=f.current.memoizedState.isDehydrated;if(d&&(rn(f,c).flags|=256),c=Ds(f,c,!1),c!==2){if(vs&&!d){f.errorRecoveryDisabledLanes|=u,Dl|=u,n=4;break t}u=fa,fa=n,u!==null&&Es(u)}n=c}if(u=!1,n!==2)continue}}if(n===1){rn(t,0),Oa(t,e,0,!0);break}t:{switch(l=t,n){case 0:case 1:throw Error(r(345));case 4:if((e&4194176)===e){Oa(l,e,Ke,!wa);break t}break;case 2:fa=null;break;case 3:case 5:break;default:throw Error(r(329))}if(l.finishedWork=a,l.finishedLanes=e,(e&62914560)===e&&(u=bs+300-Te(),10<u)){if(Oa(l,e,Ke,!wa),Ft(l,0)!==0)break t;l.timeoutHandle=Bd(gd.bind(null,l,a,fa,pi,ys,e,Ke,Dl,sn,wa,2,-0,0),u);break t}gd(l,a,fa,pi,ys,e,Ke,Dl,sn,wa,0,-0,0)}}break}while(!0);da(t)}function Es(t){fa===null?fa=t:fa.push.apply(fa,t)}function gd(t,e,a,l,n,u,c,f,d,y,D,C,E){var T=e.subtreeFlags;if((T&8192||(T&16785408)===16785408)&&(du={stylesheets:null,count:0,unsuspend:s0},nd(e),e=o0(),e!==null)){t.cancelPendingCommit=e(Sd.bind(null,t,a,l,n,c,f,d,1,C,E)),Oa(t,u,c,!y);return}Sd(t,a,l,n,c,f,d,D,C,E)}function zm(t){for(var e=t;;){var a=e.tag;if((a===0||a===11||a===15)&&e.flags&16384&&(a=e.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var l=0;l<a.length;l++){var n=a[l],u=n.getSnapshot;n=n.value;try{if(!we(u(),n))return!1}catch{return!1}}if(a=e.child,e.subtreeFlags&16384&&a!==null)a.return=e,e=a;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Oa(t,e,a,l){e&=~ps,e&=~Dl,t.suspendedLanes|=e,t.pingedLanes&=~e,l&&(t.warmLanes|=e),l=t.expirationTimes;for(var n=e;0<n;){var u=31-de(n),c=1<<u;l[u]=-1,n&=~c}a!==0&&ze(t,a,e)}function bi(){return(Bt&6)===0?(cu(0),!1):!0}function Ms(){if(ht!==null){if(Ot===0)var t=ht.return;else t=ht,Aa=Al=null,Oc(t),Wl=null,Ln=0,t=ht;for(;t!==null;)fd(t.alternate,t),t=t.return;ht=null}}function rn(t,e){t.finishedWork=null,t.finishedLanes=0;var a=t.timeoutHandle;a!==-1&&(t.timeoutHandle=-1,Km(a)),a=t.cancelPendingCommit,a!==null&&(t.cancelPendingCommit=null,a()),Ms(),Rt=t,ht=a=Ja(t.current,null),yt=e,Ot=0,Ne=null,wa=!1,cn=ve(t,e),vs=!1,sn=Ke=ps=Dl=$a=Yt=0,fa=nu=null,ys=!1,(e&8)!==0&&(e|=e&32);var l=t.entangledLanes;if(l!==0)for(t=t.entanglements,l&=e;0<l;){var n=31-de(l),u=1<<n;e|=t[n],l&=~u}return Ra=e,Qu(),a}function md(t,e){ot=null,Y.H=oa,e===Yn?(e=Ro(),Ot=3):e===Do?(e=Ro(),Ot=4):Ot=e===wf?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,Ne=e,ht===null&&(Yt=1,ri(t,Ye(e,t.current)))}function vd(){var t=Y.H;return Y.H=oa,t===null?oa:t}function pd(){var t=Y.A;return Y.A=Tm,t}function Ts(){Yt=4,wa||(yt&4194176)!==yt&&Xe.current!==null||(cn=!0),($a&134217727)===0&&(Dl&134217727)===0||Rt===null||Oa(Rt,yt,Ke,!1)}function Ds(t,e,a){var l=Bt;Bt|=2;var n=vd(),u=pd();(Rt!==t||yt!==e)&&(pi=null,rn(t,e)),e=!1;var c=Yt;t:do try{if(Ot!==0&&ht!==null){var f=ht,d=Ne;switch(Ot){case 8:Ms(),c=6;break t;case 3:case 2:case 6:Xe.current===null&&(e=!0);var y=Ot;if(Ot=0,Ne=null,on(t,f,d,y),a&&cn){c=0;break t}break;default:y=Ot,Ot=0,Ne=null,on(t,f,d,y)}}wm(),c=Yt;break}catch(D){md(t,D)}while(!0);return e&&t.shellSuspendCounter++,Aa=Al=null,Bt=l,Y.H=n,Y.A=u,ht===null&&(Rt=null,yt=0,Qu()),c}function wm(){for(;ht!==null;)yd(ht)}function Rm(t,e){var a=Bt;Bt|=2;var l=vd(),n=pd();Rt!==t||yt!==e?(pi=null,vi=Te()+500,rn(t,e)):cn=ve(t,e);t:do try{if(Ot!==0&&ht!==null){e=ht;var u=Ne;e:switch(Ot){case 1:Ot=0,Ne=null,on(t,e,u,1);break;case 2:if(zo(u)){Ot=0,Ne=null,bd(e);break}e=function(){Ot===2&&Rt===t&&(Ot=7),da(t)},u.then(e,e);break t;case 3:Ot=7;break t;case 4:Ot=5;break t;case 7:zo(u)?(Ot=0,Ne=null,bd(e)):(Ot=0,Ne=null,on(t,e,u,7));break;case 5:var c=null;switch(ht.tag){case 26:c=ht.memoizedState;case 5:case 27:var f=ht;if(!c||Jd(c)){Ot=0,Ne=null;var d=f.sibling;if(d!==null)ht=d;else{var y=f.return;y!==null?(ht=y,xi(y)):ht=null}break e}}Ot=0,Ne=null,on(t,e,u,5);break;case 6:Ot=0,Ne=null,on(t,e,u,6);break;case 8:Ms(),Yt=6;break t;default:throw Error(r(462))}}Om();break}catch(D){md(t,D)}while(!0);return Aa=Al=null,Y.H=l,Y.A=n,Bt=a,ht!==null?0:(Rt=null,yt=0,Qu(),Yt)}function Om(){for(;ht!==null&&!Cl();)yd(ht)}function yd(t){var e=Gf(t.alternate,t,Ra);t.memoizedProps=t.pendingProps,e===null?xi(t):ht=e}function bd(t){var e=t,a=e.alternate;switch(e.tag){case 15:case 0:e=Uf(a,e,e.pendingProps,e.type,void 0,yt);break;case 11:e=Uf(a,e,e.pendingProps,e.type.render,e.ref,yt);break;case 5:Oc(e);default:fd(a,e),e=ht=sd(e,Ra),e=Gf(a,e,Ra)}t.memoizedProps=t.pendingProps,e===null?xi(t):ht=e}function on(t,e,a,l){Aa=Al=null,Oc(e),Wl=null,Ln=0;var n=e.return;try{if(ym(t,n,e,a,yt)){Yt=1,ri(t,Ye(a,t.current)),ht=null;return}}catch(u){if(n!==null)throw ht=n,u;Yt=1,ri(t,Ye(a,t.current)),ht=null;return}e.flags&32768?(xt||l===1?t=!0:cn||(yt&536870912)!==0?t=!1:(wa=t=!0,(l===2||l===3||l===6)&&(l=Xe.current,l!==null&&l.tag===13&&(l.flags|=16384))),xd(e,t)):xi(e)}function xi(t){var e=t;do{if((e.flags&32768)!==0){xd(e,wa);return}t=e.return;var a=Em(e.alternate,e,Ra);if(a!==null){ht=a;return}if(e=e.sibling,e!==null){ht=e;return}ht=e=t}while(e!==null);Yt===0&&(Yt=5)}function xd(t,e){do{var a=Mm(t.alternate,t);if(a!==null){a.flags&=32767,ht=a;return}if(a=t.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!e&&(t=t.sibling,t!==null)){ht=t;return}ht=t=a}while(t!==null);Yt=6,ht=null}function Sd(t,e,a,l,n,u,c,f,d,y){var D=Y.T,C=q.p;try{q.p=2,Y.T=null,Nm(t,e,a,l,C,n,u,c,f,d,y)}finally{Y.T=D,q.p=C}}function Nm(t,e,a,l,n,u,c,f){do fn();while(zl!==null);if((Bt&6)!==0)throw Error(r(327));var d=t.finishedWork;if(l=t.finishedLanes,d===null)return null;if(t.finishedWork=null,t.finishedLanes=0,d===t.current)throw Error(r(177));t.callbackNode=null,t.callbackPriority=0,t.cancelPendingCommit=null;var y=d.lanes|d.childLanes;if(y|=mc,ia(t,l,y,u,c,f),t===Rt&&(ht=Rt=null,yt=0),(d.subtreeFlags&10256)===0&&(d.flags&10256)===0||yi||(yi=!0,xs=y,Ss=a,jm(Fe,function(){return fn(),null})),a=(d.flags&15990)!==0,(d.subtreeFlags&15990)!==0||a?(a=Y.T,Y.T=null,u=q.p,q.p=2,c=Bt,Bt|=4,xm(t,d),ed(d,t),em(qs,t.containerInfo),_i=!!Bs,qs=Bs=null,t.current=d,Ff(t,d.alternate,d),wu(),Bt=c,q.p=u,Y.T=a):t.current=d,yi?(yi=!1,zl=t,uu=l):Ad(t,y),y=t.pendingLanes,y===0&&(Wa=null),Mn(d.stateNode),da(t),e!==null)for(n=t.onRecoverableError,d=0;d<e.length;d++)y=e[d],n(y.value,{componentStack:y.stack});return(uu&3)!==0&&fn(),y=t.pendingLanes,(l&4194218)!==0&&(y&42)!==0?t===As?iu++:(iu=0,As=t):iu=0,cu(0),null}function Ad(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,Qn(e)))}function fn(){if(zl!==null){var t=zl,e=xs;xs=0;var a=Ca(uu),l=Y.T,n=q.p;try{if(q.p=32>a?32:a,Y.T=null,zl===null)var u=!1;else{a=Ss,Ss=null;var c=zl,f=uu;if(zl=null,uu=0,(Bt&6)!==0)throw Error(r(331));var d=Bt;if(Bt|=4,id(c.current),ld(c,c.current,f,a),Bt=d,cu(0,!1),fe&&typeof fe.onPostCommitFiberRoot=="function")try{fe.onPostCommitFiberRoot(cl,c)}catch{}u=!0}return u}finally{q.p=n,Y.T=l,Ad(t,e)}}return!1}function Ed(t,e,a){e=Ye(a,e),e=Vc(t.stateNode,e,2),t=Va(t,e,2),t!==null&&(jt(t,2),da(t))}function wt(t,e,a){if(t.tag===3)Ed(t,t,a);else for(;e!==null;){if(e.tag===3){Ed(e,t,a);break}else if(e.tag===1){var l=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(Wa===null||!Wa.has(l))){t=Ye(a,t),a=Df(2),l=Va(e,a,2),l!==null&&(zf(a,l,e,t),jt(l,2),da(l));break}}e=e.return}}function zs(t,e,a){var l=t.pingCache;if(l===null){l=t.pingCache=new Dm;var n=new Set;l.set(e,n)}else n=l.get(e),n===void 0&&(n=new Set,l.set(e,n));n.has(a)||(vs=!0,n.add(a),t=_m.bind(null,t,e,a),e.then(t,t))}function _m(t,e,a){var l=t.pingCache;l!==null&&l.delete(e),t.pingedLanes|=t.suspendedLanes&a,t.warmLanes&=~a,Rt===t&&(yt&a)===a&&(Yt===4||Yt===3&&(yt&62914560)===yt&&300>Te()-bs?(Bt&2)===0&&rn(t,0):ps|=a,sn===yt&&(sn=0)),da(t)}function Md(t,e){e===0&&(e=De()),t=Ha(t,e),t!==null&&(jt(t,e),da(t))}function Cm(t){var e=t.memoizedState,a=0;e!==null&&(a=e.retryLane),Md(t,a)}function Um(t,e){var a=0;switch(t.tag){case 13:var l=t.stateNode,n=t.memoizedState;n!==null&&(a=n.retryLane);break;case 19:l=t.stateNode;break;case 22:l=t.stateNode._retryCache;break;default:throw Error(r(314))}l!==null&&l.delete(e),Md(t,a)}function jm(t,e){return Ue(t,e)}var Si=null,dn=null,ws=!1,Ai=!1,Rs=!1,wl=0;function da(t){t!==dn&&t.next===null&&(dn===null?Si=dn=t:dn=dn.next=t),Ai=!0,ws||(ws=!0,Bm(Hm))}function cu(t,e){if(!Rs&&Ai){Rs=!0;do for(var a=!1,l=Si;l!==null;){if(t!==0){var n=l.pendingLanes;if(n===0)var u=0;else{var c=l.suspendedLanes,f=l.pingedLanes;u=(1<<31-de(42|t)+1)-1,u&=n&~(c&~f),u=u&201326677?u&201326677|1:u?u|2:0}u!==0&&(a=!0,zd(l,u))}else u=yt,u=Ft(l,l===Rt?u:0),(u&3)===0||ve(l,u)||(a=!0,zd(l,u));l=l.next}while(a);Rs=!1}}function Hm(){Ai=ws=!1;var t=0;wl!==0&&(Zm()&&(t=wl),wl=0);for(var e=Te(),a=null,l=Si;l!==null;){var n=l.next,u=Td(l,e);u===0?(l.next=null,a===null?Si=n:a.next=n,n===null&&(dn=a)):(a=l,(t!==0||(u&3)!==0)&&(Ai=!0)),l=n}cu(t)}function Td(t,e){for(var a=t.suspendedLanes,l=t.pingedLanes,n=t.expirationTimes,u=t.pendingLanes&-62914561;0<u;){var c=31-de(u),f=1<<c,d=n[c];d===-1?((f&a)===0||(f&l)!==0)&&(n[c]=pe(f,e)):d<=e&&(t.expiredLanes|=f),u&=~f}if(e=Rt,a=yt,a=Ft(t,t===e?a:0),l=t.callbackNode,a===0||t===e&&Ot===2||t.cancelPendingCommit!==null)return l!==null&&l!==null&&_a(l),t.callbackNode=null,t.callbackPriority=0;if((a&3)===0||ve(t,a)){if(e=a&-a,e===t.callbackPriority)return e;switch(l!==null&&_a(l),Ca(a)){case 2:case 8:a=ga;break;case 32:a=Fe;break;case 268435456:a=En;break;default:a=Fe}return l=Dd.bind(null,t),a=Ue(a,l),t.callbackPriority=e,t.callbackNode=a,e}return l!==null&&l!==null&&_a(l),t.callbackPriority=2,t.callbackNode=null,2}function Dd(t,e){var a=t.callbackNode;if(fn()&&t.callbackNode!==a)return null;var l=yt;return l=Ft(t,t===Rt?l:0),l===0?null:(hd(t,l,e),Td(t,Te()),t.callbackNode!=null&&t.callbackNode===a?Dd.bind(null,t):null)}function zd(t,e){if(fn())return null;hd(t,e,!0)}function Bm(t){km(function(){(Bt&6)!==0?Ue(Ru,t):t()})}function Os(){return wl===0&&(wl=ce()),wl}function wd(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:Hu(""+t)}function Rd(t,e){var a=e.ownerDocument.createElement("input");return a.name=e.name,a.value=e.value,t.id&&a.setAttribute("form",t.id),e.parentNode.insertBefore(a,e),t=new FormData(t),a.parentNode.removeChild(a),t}function qm(t,e,a,l,n){if(e==="submit"&&a&&a.stateNode===n){var u=wd((n[be]||null).action),c=l.submitter;c&&(e=(e=c[be]||null)?wd(e.formAction):c.getAttribute("formAction"),e!==null&&(u=e,c=null));var f=new Gu("action","action",null,l,n);t.push({event:f,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(wl!==0){var d=c?Rd(n,c):new FormData(n);Yc(a,{pending:!0,data:d,method:n.method,action:u},null,d)}}else typeof u=="function"&&(f.preventDefault(),d=c?Rd(n,c):new FormData(n),Yc(a,{pending:!0,data:d,method:n.method,action:u},u,d))},currentTarget:n}]})}}for(var Ns=0;Ns<xo.length;Ns++){var _s=xo[Ns],Ym=_s.toLowerCase(),Gm=_s[0].toUpperCase()+_s.slice(1);Ie(Ym,"on"+Gm)}Ie(mo,"onAnimationEnd"),Ie(vo,"onAnimationIteration"),Ie(po,"onAnimationStart"),Ie("dblclick","onDoubleClick"),Ie("focusin","onFocus"),Ie("focusout","onBlur"),Ie(lm,"onTransitionRun"),Ie(nm,"onTransitionStart"),Ie(um,"onTransitionCancel"),Ie(yo,"onTransitionEnd"),ql("onMouseEnter",["mouseout","mouseover"]),ql("onMouseLeave",["mouseout","mouseover"]),ql("onPointerEnter",["pointerout","pointerover"]),ql("onPointerLeave",["pointerout","pointerover"]),rl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),rl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),rl("onBeforeInput",["compositionend","keypress","textInput","paste"]),rl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),rl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),rl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var su="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Lm=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(su));function Od(t,e){e=(e&4)!==0;for(var a=0;a<t.length;a++){var l=t[a],n=l.event;l=l.listeners;t:{var u=void 0;if(e)for(var c=l.length-1;0<=c;c--){var f=l[c],d=f.instance,y=f.currentTarget;if(f=f.listener,d!==u&&n.isPropagationStopped())break t;u=f,n.currentTarget=y;try{u(n)}catch(D){si(D)}n.currentTarget=null,u=d}else for(c=0;c<l.length;c++){if(f=l[c],d=f.instance,y=f.currentTarget,f=f.listener,d!==u&&n.isPropagationStopped())break t;u=f,n.currentTarget=y;try{u(n)}catch(D){si(D)}n.currentTarget=null,u=d}}}}function vt(t,e){var a=e[Ji];a===void 0&&(a=e[Ji]=new Set);var l=t+"__bubble";a.has(l)||(Nd(e,t,2,!1),a.add(l))}function Cs(t,e,a){var l=0;e&&(l|=4),Nd(a,t,l,e)}var Ei="_reactListening"+Math.random().toString(36).slice(2);function Us(t){if(!t[Ei]){t[Ei]=!0,_r.forEach(function(a){a!=="selectionchange"&&(Lm.has(a)||Cs(a,!1,t),Cs(a,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Ei]||(e[Ei]=!0,Cs("selectionchange",!1,e))}}function Nd(t,e,a,l){switch(th(e)){case 2:var n=h0;break;case 8:n=g0;break;default:n=ks}a=n.bind(null,e,a,t),n=void 0,!ac||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(n=!0),l?n!==void 0?t.addEventListener(e,a,{capture:!0,passive:n}):t.addEventListener(e,a,!0):n!==void 0?t.addEventListener(e,a,{passive:n}):t.addEventListener(e,a,!1)}function js(t,e,a,l,n){var u=l;if((e&1)===0&&(e&2)===0&&l!==null)t:for(;;){if(l===null)return;var c=l.tag;if(c===3||c===4){var f=l.stateNode.containerInfo;if(f===n||f.nodeType===8&&f.parentNode===n)break;if(c===4)for(c=l.return;c!==null;){var d=c.tag;if((d===3||d===4)&&(d=c.stateNode.containerInfo,d===n||d.nodeType===8&&d.parentNode===n))return;c=c.return}for(;f!==null;){if(c=sl(f),c===null)return;if(d=c.tag,d===5||d===6||d===26||d===27){l=u=c;continue t}f=f.parentNode}}l=l.return}Vr(function(){var y=u,D=tc(a),C=[];t:{var E=bo.get(t);if(E!==void 0){var T=Gu,J=t;switch(t){case"keypress":if(qu(a)===0)break t;case"keydown":case"keyup":T=Ug;break;case"focusin":J="focus",T=ic;break;case"focusout":J="blur",T=ic;break;case"beforeblur":case"afterblur":T=ic;break;case"click":if(a.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":T=kr;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":T=Ag;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":T=Bg;break;case mo:case vo:case po:T=Tg;break;case yo:T=Yg;break;case"scroll":case"scrollend":T=xg;break;case"wheel":T=Lg;break;case"copy":case"cut":case"paste":T=zg;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":T=$r;break;case"toggle":case"beforetoggle":T=Qg}var it=(e&4)!==0,Gt=!it&&(t==="scroll"||t==="scrollend"),b=it?E!==null?E+"Capture":null:E;it=[];for(var v=y,A;v!==null;){var R=v;if(A=R.stateNode,R=R.tag,R!==5&&R!==26&&R!==27||A===null||b===null||(R=zn(v,b),R!=null&&it.push(ru(v,R,A))),Gt)break;v=v.return}0<it.length&&(E=new T(E,J,null,a,D),C.push({event:E,listeners:it}))}}if((e&7)===0){t:{if(E=t==="mouseover"||t==="pointerover",T=t==="mouseout"||t==="pointerout",E&&a!==Ii&&(J=a.relatedTarget||a.fromElement)&&(sl(J)||J[jl]))break t;if((T||E)&&(E=D.window===D?D:(E=D.ownerDocument)?E.defaultView||E.parentWindow:window,T?(J=a.relatedTarget||a.toElement,T=y,J=J?sl(J):null,J!==null&&(Gt=L(J),it=J.tag,J!==Gt||it!==5&&it!==27&&it!==6)&&(J=null)):(T=null,J=y),T!==J)){if(it=kr,R="onMouseLeave",b="onMouseEnter",v="mouse",(t==="pointerout"||t==="pointerover")&&(it=$r,R="onPointerLeave",b="onPointerEnter",v="pointer"),Gt=T==null?E:Dn(T),A=J==null?E:Dn(J),E=new it(R,v+"leave",T,a,D),E.target=Gt,E.relatedTarget=A,R=null,sl(D)===y&&(it=new it(b,v+"enter",J,a,D),it.target=A,it.relatedTarget=Gt,R=it),Gt=R,T&&J)e:{for(it=T,b=J,v=0,A=it;A;A=hn(A))v++;for(A=0,R=b;R;R=hn(R))A++;for(;0<v-A;)it=hn(it),v--;for(;0<A-v;)b=hn(b),A--;for(;v--;){if(it===b||b!==null&&it===b.alternate)break e;it=hn(it),b=hn(b)}it=null}else it=null;T!==null&&_d(C,E,T,it,!1),J!==null&&Gt!==null&&_d(C,Gt,J,it,!0)}}t:{if(E=y?Dn(y):window,T=E.nodeName&&E.nodeName.toLowerCase(),T==="select"||T==="input"&&E.type==="file")var Q=lo;else if(eo(E))if(no)Q=Ig;else{Q=Fg;var ft=Wg}else T=E.nodeName,!T||T.toLowerCase()!=="input"||E.type!=="checkbox"&&E.type!=="radio"?y&&Pi(y.elementType)&&(Q=lo):Q=Pg;if(Q&&(Q=Q(t,y))){ao(C,Q,a,D);break t}ft&&ft(t,E,y),t==="focusout"&&y&&E.type==="number"&&y.memoizedProps.value!=null&&Fi(E,"number",E.value)}switch(ft=y?Dn(y):window,t){case"focusin":(eo(ft)||ft.contentEditable==="true")&&(Vl=ft,dc=y,jn=null);break;case"focusout":jn=dc=Vl=null;break;case"mousedown":hc=!0;break;case"contextmenu":case"mouseup":case"dragend":hc=!1,ho(C,a,D);break;case"selectionchange":if(am)break;case"keydown":case"keyup":ho(C,a,D)}var P;if(sc)t:{switch(t){case"compositionstart":var et="onCompositionStart";break t;case"compositionend":et="onCompositionEnd";break t;case"compositionupdate":et="onCompositionUpdate";break t}et=void 0}else Ql?Ir(t,a)&&(et="onCompositionEnd"):t==="keydown"&&a.keyCode===229&&(et="onCompositionStart");et&&(Wr&&a.locale!=="ko"&&(Ql||et!=="onCompositionStart"?et==="onCompositionEnd"&&Ql&&(P=Zr()):(ja=D,lc="value"in ja?ja.value:ja.textContent,Ql=!0)),ft=Mi(y,et),0<ft.length&&(et=new Jr(et,t,null,a,D),C.push({event:et,listeners:ft}),P?et.data=P:(P=to(a),P!==null&&(et.data=P)))),(P=Zg?Kg(t,a):kg(t,a))&&(et=Mi(y,"onBeforeInput"),0<et.length&&(ft=new Jr("onBeforeInput","beforeinput",null,a,D),C.push({event:ft,listeners:et}),ft.data=P)),qm(C,t,y,a,D)}Od(C,e)})}function ru(t,e,a){return{instance:t,listener:e,currentTarget:a}}function Mi(t,e){for(var a=e+"Capture",l=[];t!==null;){var n=t,u=n.stateNode;n=n.tag,n!==5&&n!==26&&n!==27||u===null||(n=zn(t,a),n!=null&&l.unshift(ru(t,n,u)),n=zn(t,e),n!=null&&l.push(ru(t,n,u))),t=t.return}return l}function hn(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function _d(t,e,a,l,n){for(var u=e._reactName,c=[];a!==null&&a!==l;){var f=a,d=f.alternate,y=f.stateNode;if(f=f.tag,d!==null&&d===l)break;f!==5&&f!==26&&f!==27||y===null||(d=y,n?(y=zn(a,u),y!=null&&c.unshift(ru(a,y,d))):n||(y=zn(a,u),y!=null&&c.push(ru(a,y,d)))),a=a.return}c.length!==0&&t.push({event:e,listeners:c})}var Xm=/\r\n?/g,Qm=/\u0000|\uFFFD/g;function Cd(t){return(typeof t=="string"?t:""+t).replace(Xm,`
`).replace(Qm,"")}function Ud(t,e){return e=Cd(e),Cd(t)===e}function Ti(){}function Dt(t,e,a,l,n,u){switch(a){case"children":typeof l=="string"?e==="body"||e==="textarea"&&l===""||Gl(t,l):(typeof l=="number"||typeof l=="bigint")&&e!=="body"&&Gl(t,""+l);break;case"className":Cu(t,"class",l);break;case"tabIndex":Cu(t,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":Cu(t,a,l);break;case"style":Xr(t,l,u);break;case"data":if(e!=="object"){Cu(t,"data",l);break}case"src":case"href":if(l===""&&(e!=="a"||a!=="href")){t.removeAttribute(a);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(a);break}l=Hu(""+l),t.setAttribute(a,l);break;case"action":case"formAction":if(typeof l=="function"){t.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(a==="formAction"?(e!=="input"&&Dt(t,e,"name",n.name,n,null),Dt(t,e,"formEncType",n.formEncType,n,null),Dt(t,e,"formMethod",n.formMethod,n,null),Dt(t,e,"formTarget",n.formTarget,n,null)):(Dt(t,e,"encType",n.encType,n,null),Dt(t,e,"method",n.method,n,null),Dt(t,e,"target",n.target,n,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(a);break}l=Hu(""+l),t.setAttribute(a,l);break;case"onClick":l!=null&&(t.onclick=Ti);break;case"onScroll":l!=null&&vt("scroll",t);break;case"onScrollEnd":l!=null&&vt("scrollend",t);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(r(61));if(a=l.__html,a!=null){if(n.children!=null)throw Error(r(60));t.innerHTML=a}}break;case"multiple":t.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":t.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){t.removeAttribute("xlink:href");break}a=Hu(""+l),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(a,""+l):t.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(a,""):t.removeAttribute(a);break;case"capture":case"download":l===!0?t.setAttribute(a,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(a,l):t.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?t.setAttribute(a,l):t.removeAttribute(a);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?t.removeAttribute(a):t.setAttribute(a,l);break;case"popover":vt("beforetoggle",t),vt("toggle",t),_u(t,"popover",l);break;case"xlinkActuate":va(t,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":va(t,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":va(t,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":va(t,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":va(t,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":va(t,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":va(t,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":va(t,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":va(t,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":_u(t,"is",l);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=yg.get(a)||a,_u(t,a,l))}}function Hs(t,e,a,l,n,u){switch(a){case"style":Xr(t,l,u);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(r(61));if(a=l.__html,a!=null){if(n.children!=null)throw Error(r(60));t.innerHTML=a}}break;case"children":typeof l=="string"?Gl(t,l):(typeof l=="number"||typeof l=="bigint")&&Gl(t,""+l);break;case"onScroll":l!=null&&vt("scroll",t);break;case"onScrollEnd":l!=null&&vt("scrollend",t);break;case"onClick":l!=null&&(t.onclick=Ti);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Cr.hasOwnProperty(a))t:{if(a[0]==="o"&&a[1]==="n"&&(n=a.endsWith("Capture"),e=a.slice(2,n?a.length-7:void 0),u=t[be]||null,u=u!=null?u[a]:null,typeof u=="function"&&t.removeEventListener(e,u,n),typeof l=="function")){typeof u!="function"&&u!==null&&(a in t?t[a]=null:t.hasAttribute(a)&&t.removeAttribute(a)),t.addEventListener(e,l,n);break t}a in t?t[a]=l:l===!0?t.setAttribute(a,""):_u(t,a,l)}}}function ie(t,e,a){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":vt("error",t),vt("load",t);var l=!1,n=!1,u;for(u in a)if(a.hasOwnProperty(u)){var c=a[u];if(c!=null)switch(u){case"src":l=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(r(137,e));default:Dt(t,e,u,c,a,null)}}n&&Dt(t,e,"srcSet",a.srcSet,a,null),l&&Dt(t,e,"src",a.src,a,null);return;case"input":vt("invalid",t);var f=u=c=n=null,d=null,y=null;for(l in a)if(a.hasOwnProperty(l)){var D=a[l];if(D!=null)switch(l){case"name":n=D;break;case"type":c=D;break;case"checked":d=D;break;case"defaultChecked":y=D;break;case"value":u=D;break;case"defaultValue":f=D;break;case"children":case"dangerouslySetInnerHTML":if(D!=null)throw Error(r(137,e));break;default:Dt(t,e,l,D,a,null)}}qr(t,u,f,d,y,c,n,!1),Uu(t);return;case"select":vt("invalid",t),l=c=u=null;for(n in a)if(a.hasOwnProperty(n)&&(f=a[n],f!=null))switch(n){case"value":u=f;break;case"defaultValue":c=f;break;case"multiple":l=f;default:Dt(t,e,n,f,a,null)}e=u,a=c,t.multiple=!!l,e!=null?Yl(t,!!l,e,!1):a!=null&&Yl(t,!!l,a,!0);return;case"textarea":vt("invalid",t),u=n=l=null;for(c in a)if(a.hasOwnProperty(c)&&(f=a[c],f!=null))switch(c){case"value":l=f;break;case"defaultValue":n=f;break;case"children":u=f;break;case"dangerouslySetInnerHTML":if(f!=null)throw Error(r(91));break;default:Dt(t,e,c,f,a,null)}Gr(t,l,n,u),Uu(t);return;case"option":for(d in a)if(a.hasOwnProperty(d)&&(l=a[d],l!=null))switch(d){case"selected":t.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:Dt(t,e,d,l,a,null)}return;case"dialog":vt("cancel",t),vt("close",t);break;case"iframe":case"object":vt("load",t);break;case"video":case"audio":for(l=0;l<su.length;l++)vt(su[l],t);break;case"image":vt("error",t),vt("load",t);break;case"details":vt("toggle",t);break;case"embed":case"source":case"link":vt("error",t),vt("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(y in a)if(a.hasOwnProperty(y)&&(l=a[y],l!=null))switch(y){case"children":case"dangerouslySetInnerHTML":throw Error(r(137,e));default:Dt(t,e,y,l,a,null)}return;default:if(Pi(e)){for(D in a)a.hasOwnProperty(D)&&(l=a[D],l!==void 0&&Hs(t,e,D,l,a,void 0));return}}for(f in a)a.hasOwnProperty(f)&&(l=a[f],l!=null&&Dt(t,e,f,l,a,null))}function Vm(t,e,a,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,u=null,c=null,f=null,d=null,y=null,D=null;for(T in a){var C=a[T];if(a.hasOwnProperty(T)&&C!=null)switch(T){case"checked":break;case"value":break;case"defaultValue":d=C;default:l.hasOwnProperty(T)||Dt(t,e,T,null,l,C)}}for(var E in l){var T=l[E];if(C=a[E],l.hasOwnProperty(E)&&(T!=null||C!=null))switch(E){case"type":u=T;break;case"name":n=T;break;case"checked":y=T;break;case"defaultChecked":D=T;break;case"value":c=T;break;case"defaultValue":f=T;break;case"children":case"dangerouslySetInnerHTML":if(T!=null)throw Error(r(137,e));break;default:T!==C&&Dt(t,e,E,T,l,C)}}Wi(t,c,f,d,y,D,u,n);return;case"select":T=c=f=E=null;for(u in a)if(d=a[u],a.hasOwnProperty(u)&&d!=null)switch(u){case"value":break;case"multiple":T=d;default:l.hasOwnProperty(u)||Dt(t,e,u,null,l,d)}for(n in l)if(u=l[n],d=a[n],l.hasOwnProperty(n)&&(u!=null||d!=null))switch(n){case"value":E=u;break;case"defaultValue":f=u;break;case"multiple":c=u;default:u!==d&&Dt(t,e,n,u,l,d)}e=f,a=c,l=T,E!=null?Yl(t,!!a,E,!1):!!l!=!!a&&(e!=null?Yl(t,!!a,e,!0):Yl(t,!!a,a?[]:"",!1));return;case"textarea":T=E=null;for(f in a)if(n=a[f],a.hasOwnProperty(f)&&n!=null&&!l.hasOwnProperty(f))switch(f){case"value":break;case"children":break;default:Dt(t,e,f,null,l,n)}for(c in l)if(n=l[c],u=a[c],l.hasOwnProperty(c)&&(n!=null||u!=null))switch(c){case"value":E=n;break;case"defaultValue":T=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(r(91));break;default:n!==u&&Dt(t,e,c,n,l,u)}Yr(t,E,T);return;case"option":for(var J in a)if(E=a[J],a.hasOwnProperty(J)&&E!=null&&!l.hasOwnProperty(J))switch(J){case"selected":t.selected=!1;break;default:Dt(t,e,J,null,l,E)}for(d in l)if(E=l[d],T=a[d],l.hasOwnProperty(d)&&E!==T&&(E!=null||T!=null))switch(d){case"selected":t.selected=E&&typeof E!="function"&&typeof E!="symbol";break;default:Dt(t,e,d,E,l,T)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var it in a)E=a[it],a.hasOwnProperty(it)&&E!=null&&!l.hasOwnProperty(it)&&Dt(t,e,it,null,l,E);for(y in l)if(E=l[y],T=a[y],l.hasOwnProperty(y)&&E!==T&&(E!=null||T!=null))switch(y){case"children":case"dangerouslySetInnerHTML":if(E!=null)throw Error(r(137,e));break;default:Dt(t,e,y,E,l,T)}return;default:if(Pi(e)){for(var Gt in a)E=a[Gt],a.hasOwnProperty(Gt)&&E!==void 0&&!l.hasOwnProperty(Gt)&&Hs(t,e,Gt,void 0,l,E);for(D in l)E=l[D],T=a[D],!l.hasOwnProperty(D)||E===T||E===void 0&&T===void 0||Hs(t,e,D,E,l,T);return}}for(var b in a)E=a[b],a.hasOwnProperty(b)&&E!=null&&!l.hasOwnProperty(b)&&Dt(t,e,b,null,l,E);for(C in l)E=l[C],T=a[C],!l.hasOwnProperty(C)||E===T||E==null&&T==null||Dt(t,e,C,E,l,T)}var Bs=null,qs=null;function Di(t){return t.nodeType===9?t:t.ownerDocument}function jd(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Hd(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function Ys(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var Gs=null;function Zm(){var t=window.event;return t&&t.type==="popstate"?t===Gs?!1:(Gs=t,!0):(Gs=null,!1)}var Bd=typeof setTimeout=="function"?setTimeout:void 0,Km=typeof clearTimeout=="function"?clearTimeout:void 0,qd=typeof Promise=="function"?Promise:void 0,km=typeof queueMicrotask=="function"?queueMicrotask:typeof qd<"u"?function(t){return qd.resolve(null).then(t).catch(Jm)}:Bd;function Jm(t){setTimeout(function(){throw t})}function Ls(t,e){var a=e,l=0;do{var n=a.nextSibling;if(t.removeChild(a),n&&n.nodeType===8)if(a=n.data,a==="/$"){if(l===0){t.removeChild(n),pu(e);return}l--}else a!=="$"&&a!=="$?"&&a!=="$!"||l++;a=n}while(a);pu(e)}function Xs(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var a=e;switch(e=e.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":Xs(a),$i(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}t.removeChild(a)}}function $m(t,e,a,l){for(;t.nodeType===1;){var n=a;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!l&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(l){if(!t[Tn])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(u=t.getAttribute("rel"),u==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(u!==n.rel||t.getAttribute("href")!==(n.href==null?null:n.href)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||t.getAttribute("title")!==(n.title==null?null:n.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(u=t.getAttribute("src"),(u!==(n.src==null?null:n.src)||t.getAttribute("type")!==(n.type==null?null:n.type)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&u&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var u=n.name==null?null:""+n.name;if(n.type==="hidden"&&t.getAttribute("name")===u)return t}else return t;if(t=aa(t.nextSibling),t===null)break}return null}function Wm(t,e,a){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!a||(t=aa(t.nextSibling),t===null))return null;return t}function aa(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}function Yd(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var a=t.data;if(a==="$"||a==="$!"||a==="$?"){if(e===0)return t;e--}else a==="/$"&&e++}t=t.previousSibling}return null}function Gd(t,e,a){switch(e=Di(a),t){case"html":if(t=e.documentElement,!t)throw Error(r(452));return t;case"head":if(t=e.head,!t)throw Error(r(453));return t;case"body":if(t=e.body,!t)throw Error(r(454));return t;default:throw Error(r(451))}}var ke=new Map,Ld=new Set;function zi(t){return typeof t.getRootNode=="function"?t.getRootNode():t.ownerDocument}var Na=q.d;q.d={f:Fm,r:Pm,D:Im,C:t0,L:e0,m:a0,X:n0,S:l0,M:u0};function Fm(){var t=Na.f(),e=bi();return t||e}function Pm(t){var e=Hl(t);e!==null&&e.tag===5&&e.type==="form"?gf(e):Na.r(t)}var gn=typeof document>"u"?null:document;function Xd(t,e,a){var l=gn;if(l&&typeof e=="string"&&e){var n=Be(e);n='link[rel="'+t+'"][href="'+n+'"]',typeof a=="string"&&(n+='[crossorigin="'+a+'"]'),Ld.has(n)||(Ld.add(n),t={rel:t,crossOrigin:a,href:e},l.querySelector(n)===null&&(e=l.createElement("link"),ie(e,"link",t),Pt(e),l.head.appendChild(e)))}}function Im(t){Na.D(t),Xd("dns-prefetch",t,null)}function t0(t,e){Na.C(t,e),Xd("preconnect",t,e)}function e0(t,e,a){Na.L(t,e,a);var l=gn;if(l&&t&&e){var n='link[rel="preload"][as="'+Be(e)+'"]';e==="image"&&a&&a.imageSrcSet?(n+='[imagesrcset="'+Be(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(n+='[imagesizes="'+Be(a.imageSizes)+'"]')):n+='[href="'+Be(t)+'"]';var u=n;switch(e){case"style":u=mn(t);break;case"script":u=vn(t)}ke.has(u)||(t=$({rel:"preload",href:e==="image"&&a&&a.imageSrcSet?void 0:t,as:e},a),ke.set(u,t),l.querySelector(n)!==null||e==="style"&&l.querySelector(ou(u))||e==="script"&&l.querySelector(fu(u))||(e=l.createElement("link"),ie(e,"link",t),Pt(e),l.head.appendChild(e)))}}function a0(t,e){Na.m(t,e);var a=gn;if(a&&t){var l=e&&typeof e.as=="string"?e.as:"script",n='link[rel="modulepreload"][as="'+Be(l)+'"][href="'+Be(t)+'"]',u=n;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=vn(t)}if(!ke.has(u)&&(t=$({rel:"modulepreload",href:t},e),ke.set(u,t),a.querySelector(n)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(fu(u)))return}l=a.createElement("link"),ie(l,"link",t),Pt(l),a.head.appendChild(l)}}}function l0(t,e,a){Na.S(t,e,a);var l=gn;if(l&&t){var n=Bl(l).hoistableStyles,u=mn(t);e=e||"default";var c=n.get(u);if(!c){var f={loading:0,preload:null};if(c=l.querySelector(ou(u)))f.loading=5;else{t=$({rel:"stylesheet",href:t,"data-precedence":e},a),(a=ke.get(u))&&Qs(t,a);var d=c=l.createElement("link");Pt(d),ie(d,"link",t),d._p=new Promise(function(y,D){d.onload=y,d.onerror=D}),d.addEventListener("load",function(){f.loading|=1}),d.addEventListener("error",function(){f.loading|=2}),f.loading|=4,wi(c,e,l)}c={type:"stylesheet",instance:c,count:1,state:f},n.set(u,c)}}}function n0(t,e){Na.X(t,e);var a=gn;if(a&&t){var l=Bl(a).hoistableScripts,n=vn(t),u=l.get(n);u||(u=a.querySelector(fu(n)),u||(t=$({src:t,async:!0},e),(e=ke.get(n))&&Vs(t,e),u=a.createElement("script"),Pt(u),ie(u,"link",t),a.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},l.set(n,u))}}function u0(t,e){Na.M(t,e);var a=gn;if(a&&t){var l=Bl(a).hoistableScripts,n=vn(t),u=l.get(n);u||(u=a.querySelector(fu(n)),u||(t=$({src:t,async:!0,type:"module"},e),(e=ke.get(n))&&Vs(t,e),u=a.createElement("script"),Pt(u),ie(u,"link",t),a.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},l.set(n,u))}}function Qd(t,e,a,l){var n=(n=We.current)?zi(n):null;if(!n)throw Error(r(446));switch(t){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(e=mn(a.href),a=Bl(n).hoistableStyles,l=a.get(e),l||(l={type:"style",instance:null,count:0,state:null},a.set(e,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){t=mn(a.href);var u=Bl(n).hoistableStyles,c=u.get(t);if(c||(n=n.ownerDocument||n,c={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(t,c),(u=n.querySelector(ou(t)))&&!u._p&&(c.instance=u,c.state.loading=5),ke.has(t)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},ke.set(t,a),u||i0(n,t,a,c.state))),e&&l===null)throw Error(r(528,""));return c}if(e&&l!==null)throw Error(r(529,""));return null;case"script":return e=a.async,a=a.src,typeof a=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=vn(a),a=Bl(n).hoistableScripts,l=a.get(e),l||(l={type:"script",instance:null,count:0,state:null},a.set(e,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(r(444,t))}}function mn(t){return'href="'+Be(t)+'"'}function ou(t){return'link[rel="stylesheet"]['+t+"]"}function Vd(t){return $({},t,{"data-precedence":t.precedence,precedence:null})}function i0(t,e,a,l){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?l.loading=1:(e=t.createElement("link"),l.preload=e,e.addEventListener("load",function(){return l.loading|=1}),e.addEventListener("error",function(){return l.loading|=2}),ie(e,"link",a),Pt(e),t.head.appendChild(e))}function vn(t){return'[src="'+Be(t)+'"]'}function fu(t){return"script[async]"+t}function Zd(t,e,a){if(e.count++,e.instance===null)switch(e.type){case"style":var l=t.querySelector('style[data-href~="'+Be(a.href)+'"]');if(l)return e.instance=l,Pt(l),l;var n=$({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return l=(t.ownerDocument||t).createElement("style"),Pt(l),ie(l,"style",n),wi(l,a.precedence,t),e.instance=l;case"stylesheet":n=mn(a.href);var u=t.querySelector(ou(n));if(u)return e.state.loading|=4,e.instance=u,Pt(u),u;l=Vd(a),(n=ke.get(n))&&Qs(l,n),u=(t.ownerDocument||t).createElement("link"),Pt(u);var c=u;return c._p=new Promise(function(f,d){c.onload=f,c.onerror=d}),ie(u,"link",l),e.state.loading|=4,wi(u,a.precedence,t),e.instance=u;case"script":return u=vn(a.src),(n=t.querySelector(fu(u)))?(e.instance=n,Pt(n),n):(l=a,(n=ke.get(u))&&(l=$({},a),Vs(l,n)),t=t.ownerDocument||t,n=t.createElement("script"),Pt(n),ie(n,"link",l),t.head.appendChild(n),e.instance=n);case"void":return null;default:throw Error(r(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(l=e.instance,e.state.loading|=4,wi(l,a.precedence,t));return e.instance}function wi(t,e,a){for(var l=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=l.length?l[l.length-1]:null,u=n,c=0;c<l.length;c++){var f=l[c];if(f.dataset.precedence===e)u=f;else if(u!==n)break}u?u.parentNode.insertBefore(t,u.nextSibling):(e=a.nodeType===9?a.head:a,e.insertBefore(t,e.firstChild))}function Qs(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function Vs(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Ri=null;function Kd(t,e,a){if(Ri===null){var l=new Map,n=Ri=new Map;n.set(a,l)}else n=Ri,l=n.get(a),l||(l=new Map,n.set(a,l));if(l.has(t))return l;for(l.set(t,null),a=a.getElementsByTagName(t),n=0;n<a.length;n++){var u=a[n];if(!(u[Tn]||u[Vt]||t==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var c=u.getAttribute(e)||"";c=t+c;var f=l.get(c);f?f.push(u):l.set(c,[u])}}return l}function kd(t,e,a){t=t.ownerDocument||t,t.head.insertBefore(a,e==="title"?t.querySelector("head > title"):null)}function c0(t,e,a){if(a===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Jd(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var du=null;function s0(){}function r0(t,e,a){if(du===null)throw Error(r(475));var l=du;if(e.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var n=mn(a.href),u=t.querySelector(ou(n));if(u){t=u._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(l.count++,l=Oi.bind(l),t.then(l,l)),e.state.loading|=4,e.instance=u,Pt(u);return}u=t.ownerDocument||t,a=Vd(a),(n=ke.get(n))&&Qs(a,n),u=u.createElement("link"),Pt(u);var c=u;c._p=new Promise(function(f,d){c.onload=f,c.onerror=d}),ie(u,"link",a),e.instance=u}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(l.count++,e=Oi.bind(l),t.addEventListener("load",e),t.addEventListener("error",e))}}function o0(){if(du===null)throw Error(r(475));var t=du;return t.stylesheets&&t.count===0&&Zs(t,t.stylesheets),0<t.count?function(e){var a=setTimeout(function(){if(t.stylesheets&&Zs(t,t.stylesheets),t.unsuspend){var l=t.unsuspend;t.unsuspend=null,l()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(a)}}:null}function Oi(){if(this.count--,this.count===0){if(this.stylesheets)Zs(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Ni=null;function Zs(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Ni=new Map,e.forEach(f0,t),Ni=null,Oi.call(t))}function f0(t,e){if(!(e.state.loading&4)){var a=Ni.get(t);if(a)var l=a.get(null);else{a=new Map,Ni.set(t,a);for(var n=t.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<n.length;u++){var c=n[u];(c.nodeName==="LINK"||c.getAttribute("media")!=="not all")&&(a.set(c.dataset.precedence,c),l=c)}l&&a.set(null,l)}n=e.instance,c=n.getAttribute("data-precedence"),u=a.get(c)||l,u===l&&a.set(null,n),a.set(c,n),this.count++,l=Oi.bind(this),n.addEventListener("load",l),n.addEventListener("error",l),u?u.parentNode.insertBefore(n,u.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(n,t.firstChild)),e.state.loading|=4}}var hu={$$typeof:X,Provider:null,Consumer:null,_currentValue:ct,_currentValue2:ct,_threadCount:0};function d0(t,e,a,l,n,u,c,f){this.tag=1,this.containerInfo=t,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=ua(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.finishedLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ua(0),this.hiddenUpdates=ua(null),this.identifierPrefix=l,this.onUncaughtError=n,this.onCaughtError=u,this.onRecoverableError=c,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=f,this.incompleteTransitions=new Map}function $d(t,e,a,l,n,u,c,f,d,y,D,C){return t=new d0(t,e,a,c,f,d,y,C),e=1,u===!0&&(e|=24),u=Ze(3,null,null,e),t.current=u,u.stateNode=t,e=Ec(),e.refCount++,t.pooledCache=e,e.refCount++,u.memoizedState={element:l,isDehydrated:a,cache:e},ls(u),t}function Wd(t){return t?(t=kl,t):kl}function Fd(t,e,a,l,n,u){n=Wd(n),l.context===null?l.context=n:l.pendingContext=n,l=Qa(e),l.payload={element:a},u=u===void 0?null:u,u!==null&&(l.callback=u),a=Va(t,l,e),a!==null&&(ge(a,t,e),Wn(a,t,e))}function Pd(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var a=t.retryLane;t.retryLane=a!==0&&a<e?a:e}}function Ks(t,e){Pd(t,e),(t=t.alternate)&&Pd(t,e)}function Id(t){if(t.tag===13){var e=Ha(t,67108864);e!==null&&ge(e,t,67108864),Ks(t,67108864)}}var _i=!0;function h0(t,e,a,l){var n=Y.T;Y.T=null;var u=q.p;try{q.p=2,ks(t,e,a,l)}finally{q.p=u,Y.T=n}}function g0(t,e,a,l){var n=Y.T;Y.T=null;var u=q.p;try{q.p=8,ks(t,e,a,l)}finally{q.p=u,Y.T=n}}function ks(t,e,a,l){if(_i){var n=Js(l);if(n===null)js(t,e,l,Ci,a),eh(t,l);else if(v0(n,t,e,a,l))l.stopPropagation();else if(eh(t,l),e&4&&-1<m0.indexOf(t)){for(;n!==null;){var u=Hl(n);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var c=Xt(u.pendingLanes);if(c!==0){var f=u;for(f.pendingLanes|=2,f.entangledLanes|=2;c;){var d=1<<31-de(c);f.entanglements[1]|=d,c&=~d}da(u),(Bt&6)===0&&(vi=Te()+500,cu(0))}}break;case 13:f=Ha(u,2),f!==null&&ge(f,u,2),bi(),Ks(u,2)}if(u=Js(l),u===null&&js(t,e,l,Ci,a),u===n)break;n=u}n!==null&&l.stopPropagation()}else js(t,e,l,null,a)}}function Js(t){return t=tc(t),$s(t)}var Ci=null;function $s(t){if(Ci=null,t=sl(t),t!==null){var e=L(t);if(e===null)t=null;else{var a=e.tag;if(a===13){if(t=lt(e),t!==null)return t;t=null}else if(a===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return Ci=t,null}function th(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Zi()){case Ru:return 2;case ga:return 8;case Fe:case il:return 32;case En:return 268435456;default:return 32}default:return 32}}var Ws=!1,Fa=null,Pa=null,Ia=null,gu=new Map,mu=new Map,tl=[],m0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function eh(t,e){switch(t){case"focusin":case"focusout":Fa=null;break;case"dragenter":case"dragleave":Pa=null;break;case"mouseover":case"mouseout":Ia=null;break;case"pointerover":case"pointerout":gu.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":mu.delete(e.pointerId)}}function vu(t,e,a,l,n,u){return t===null||t.nativeEvent!==u?(t={blockedOn:e,domEventName:a,eventSystemFlags:l,nativeEvent:u,targetContainers:[n]},e!==null&&(e=Hl(e),e!==null&&Id(e)),t):(t.eventSystemFlags|=l,e=t.targetContainers,n!==null&&e.indexOf(n)===-1&&e.push(n),t)}function v0(t,e,a,l,n){switch(e){case"focusin":return Fa=vu(Fa,t,e,a,l,n),!0;case"dragenter":return Pa=vu(Pa,t,e,a,l,n),!0;case"mouseover":return Ia=vu(Ia,t,e,a,l,n),!0;case"pointerover":var u=n.pointerId;return gu.set(u,vu(gu.get(u)||null,t,e,a,l,n)),!0;case"gotpointercapture":return u=n.pointerId,mu.set(u,vu(mu.get(u)||null,t,e,a,l,n)),!0}return!1}function ah(t){var e=sl(t.target);if(e!==null){var a=L(e);if(a!==null){if(e=a.tag,e===13){if(e=lt(a),e!==null){t.blockedOn=e,ca(t.priority,function(){if(a.tag===13){var l=_e(),n=Ha(a,l);n!==null&&ge(n,a,l),Ks(a,l)}});return}}else if(e===3&&a.stateNode.current.memoizedState.isDehydrated){t.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Ui(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var a=Js(t.nativeEvent);if(a===null){a=t.nativeEvent;var l=new a.constructor(a.type,a);Ii=l,a.target.dispatchEvent(l),Ii=null}else return e=Hl(a),e!==null&&Id(e),t.blockedOn=a,!1;e.shift()}return!0}function lh(t,e,a){Ui(t)&&a.delete(e)}function p0(){Ws=!1,Fa!==null&&Ui(Fa)&&(Fa=null),Pa!==null&&Ui(Pa)&&(Pa=null),Ia!==null&&Ui(Ia)&&(Ia=null),gu.forEach(lh),mu.forEach(lh)}function ji(t,e){t.blockedOn===e&&(t.blockedOn=null,Ws||(Ws=!0,i.unstable_scheduleCallback(i.unstable_NormalPriority,p0)))}var Hi=null;function nh(t){Hi!==t&&(Hi=t,i.unstable_scheduleCallback(i.unstable_NormalPriority,function(){Hi===t&&(Hi=null);for(var e=0;e<t.length;e+=3){var a=t[e],l=t[e+1],n=t[e+2];if(typeof l!="function"){if($s(l||a)===null)continue;break}var u=Hl(a);u!==null&&(t.splice(e,3),e-=3,Yc(u,{pending:!0,data:n,method:a.method,action:l},l,n))}}))}function pu(t){function e(d){return ji(d,t)}Fa!==null&&ji(Fa,t),Pa!==null&&ji(Pa,t),Ia!==null&&ji(Ia,t),gu.forEach(e),mu.forEach(e);for(var a=0;a<tl.length;a++){var l=tl[a];l.blockedOn===t&&(l.blockedOn=null)}for(;0<tl.length&&(a=tl[0],a.blockedOn===null);)ah(a),a.blockedOn===null&&tl.shift();if(a=(t.ownerDocument||t).$$reactFormReplay,a!=null)for(l=0;l<a.length;l+=3){var n=a[l],u=a[l+1],c=n[be]||null;if(typeof u=="function")c||nh(a);else if(c){var f=null;if(u&&u.hasAttribute("formAction")){if(n=u,c=u[be]||null)f=c.formAction;else if($s(n)!==null)continue}else f=c.action;typeof f=="function"?a[l+1]=f:(a.splice(l,3),l-=3),nh(a)}}}function Fs(t){this._internalRoot=t}Bi.prototype.render=Fs.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(r(409));var a=e.current,l=_e();Fd(a,l,t,e,null,null)},Bi.prototype.unmount=Fs.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;t.tag===0&&fn(),Fd(t.current,2,null,t,null,null),bi(),e[jl]=null}};function Bi(t){this._internalRoot=t}Bi.prototype.unstable_scheduleHydration=function(t){if(t){var e=Ua();t={blockedOn:null,target:t,priority:e};for(var a=0;a<tl.length&&e!==0&&e<tl[a].priority;a++);tl.splice(a,0,t),a===0&&ah(t)}};var uh=s.version;if(uh!=="19.0.0")throw Error(r(527,uh,"19.0.0"));q.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(r(188)):(t=Object.keys(t).join(","),Error(r(268,t)));return t=U(e),t=t!==null?k(t):null,t=t===null?null:t.stateNode,t};var y0={bundleType:0,version:"19.0.0",rendererPackageName:"react-dom",currentDispatcherRef:Y,findFiberByHostInstance:sl,reconcilerVersion:"19.0.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var qi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!qi.isDisabled&&qi.supportsFiber)try{cl=qi.inject(y0),fe=qi}catch{}}return bu.createRoot=function(t,e){if(!h(t))throw Error(r(299));var a=!1,l="",n=Af,u=Ef,c=Mf,f=null;return e!=null&&(e.unstable_strictMode===!0&&(a=!0),e.identifierPrefix!==void 0&&(l=e.identifierPrefix),e.onUncaughtError!==void 0&&(n=e.onUncaughtError),e.onCaughtError!==void 0&&(u=e.onCaughtError),e.onRecoverableError!==void 0&&(c=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(f=e.unstable_transitionCallbacks)),e=$d(t,1,!1,null,null,a,l,n,u,c,f,null),t[jl]=e.current,Us(t.nodeType===8?t.parentNode:t),new Fs(e)},bu.hydrateRoot=function(t,e,a){if(!h(t))throw Error(r(299));var l=!1,n="",u=Af,c=Ef,f=Mf,d=null,y=null;return a!=null&&(a.unstable_strictMode===!0&&(l=!0),a.identifierPrefix!==void 0&&(n=a.identifierPrefix),a.onUncaughtError!==void 0&&(u=a.onUncaughtError),a.onCaughtError!==void 0&&(c=a.onCaughtError),a.onRecoverableError!==void 0&&(f=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(d=a.unstable_transitionCallbacks),a.formState!==void 0&&(y=a.formState)),e=$d(t,1,!0,e,a??null,l,n,u,c,f,d,y),e.context=Wd(null),a=e.current,l=_e(),n=Qa(l),n.callback=null,Va(a,n,l),e.current.lanes=l,jt(e,l),da(e),t[jl]=e.current,Us(t),new Bi(e)},bu.version="19.0.0",bu}var mh;function w0(){if(mh)return tr.exports;mh=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(s){console.error(s)}}return i(),tr.exports=z0(),tr.exports}var R0=w0();/**
 * @license lucide-react v0.477.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const O0=i=>i.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Hh=(...i)=>i.filter((s,o,r)=>!!s&&s.trim()!==""&&r.indexOf(s)===o).join(" ").trim();/**
 * @license lucide-react v0.477.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var N0={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.477.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _0=w.forwardRef(({color:i="currentColor",size:s=24,strokeWidth:o=2,absoluteStrokeWidth:r,className:h="",children:m,iconNode:S,...M},x)=>w.createElement("svg",{ref:x,...N0,width:s,height:s,stroke:i,strokeWidth:r?Number(o)*24/Number(s):o,className:Hh("lucide",h),...M},[...S.map(([p,N])=>w.createElement(p,N)),...Array.isArray(m)?m:[m]]));/**
 * @license lucide-react v0.477.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jt=(i,s)=>{const o=w.forwardRef(({className:r,...h},m)=>w.createElement(_0,{ref:m,iconNode:s,className:Hh(`lucide-${O0(i)}`,r),...h}));return o.displayName=`${i}`,o};/**
 * @license lucide-react v0.477.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C0=[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]],vh=Jt("Bot",C0);/**
 * @license lucide-react v0.477.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const U0=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],j0=Jt("Calendar",U0);/**
 * @license lucide-react v0.477.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const H0=[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]],B0=Jt("Copy",H0);/**
 * @license lucide-react v0.477.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const q0=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]],ph=Jt("Grid3x3",q0);/**
 * @license lucide-react v0.477.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Y0=[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]],G0=Jt("Menu",Y0);/**
 * @license lucide-react v0.477.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const L0=[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]],X0=Jt("MessageSquare",L0);/**
 * @license lucide-react v0.477.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Q0=[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]],yh=Jt("Mic",Q0);/**
 * @license lucide-react v0.477.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const V0=[["path",{d:"M13.234 20.252 21 12.3",key:"1cbrk9"}],["path",{d:"m16 6-8.414 8.586a2 2 0 0 0 0 2.828 2 2 0 0 0 2.828 0l8.414-8.586a4 4 0 0 0 0-5.656 4 4 0 0 0-5.656 0l-8.415 8.585a6 6 0 1 0 8.486 8.486",key:"1pkts6"}]],bh=Jt("Paperclip",V0);/**
 * @license lucide-react v0.477.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Z0=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],K0=Jt("Plus",Z0);/**
 * @license lucide-react v0.477.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const k0=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]],J0=Jt("RotateCcw",k0);/**
 * @license lucide-react v0.477.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $0=[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]],W0=Jt("Search",$0);/**
 * @license lucide-react v0.477.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const F0=[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]],P0=Jt("Send",F0);/**
 * @license lucide-react v0.477.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const I0=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],tv=Jt("Settings",I0);/**
 * @license lucide-react v0.477.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ev=[["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["polyline",{points:"16 6 12 2 8 6",key:"m901s6"}],["line",{x1:"12",x2:"12",y1:"2",y2:"15",key:"1p0rca"}]],av=Jt("Share",ev);/**
 * @license lucide-react v0.477.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lv=[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]],nr=Jt("Sparkles",lv);/**
 * @license lucide-react v0.477.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nv=[["path",{d:"M17 14V2",key:"8ymqnk"}],["path",{d:"M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22a3.13 3.13 0 0 1-3-3.88Z",key:"m61m77"}]],uv=Jt("ThumbsDown",nv);/**
 * @license lucide-react v0.477.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const iv=[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2a3.13 3.13 0 0 1 3 3.88Z",key:"emmmcr"}]],cv=Jt("ThumbsUp",iv);/**
 * @license lucide-react v0.477.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sv=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],xh=Jt("User",sv);/**
 * @license lucide-react v0.477.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rv=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],ov=Jt("X",rv);/**
 * @license lucide-react v0.477.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fv=[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]],dv=Jt("Zap",fv);function Bh(i){var s,o,r="";if(typeof i=="string"||typeof i=="number")r+=i;else if(typeof i=="object")if(Array.isArray(i)){var h=i.length;for(s=0;s<h;s++)i[s]&&(o=Bh(i[s]))&&(r&&(r+=" "),r+=o)}else for(o in i)i[o]&&(r&&(r+=" "),r+=o);return r}function hv(){for(var i,s,o=0,r="",h=arguments.length;o<h;o++)(i=arguments[o])&&(s=Bh(i))&&(r&&(r+=" "),r+=s);return r}const Sr="-",gv=i=>{const s=vv(i),{conflictingClassGroups:o,conflictingClassGroupModifiers:r}=i;return{getClassGroupId:S=>{const M=S.split(Sr);return M[0]===""&&M.length!==1&&M.shift(),qh(M,s)||mv(S)},getConflictingClassGroupIds:(S,M)=>{const x=o[S]||[];return M&&r[S]?[...x,...r[S]]:x}}},qh=(i,s)=>{var S;if(i.length===0)return s.classGroupId;const o=i[0],r=s.nextPart.get(o),h=r?qh(i.slice(1),r):void 0;if(h)return h;if(s.validators.length===0)return;const m=i.join(Sr);return(S=s.validators.find(({validator:M})=>M(m)))==null?void 0:S.classGroupId},Sh=/^\[(.+)\]$/,mv=i=>{if(Sh.test(i)){const s=Sh.exec(i)[1],o=s==null?void 0:s.substring(0,s.indexOf(":"));if(o)return"arbitrary.."+o}},vv=i=>{const{theme:s,classGroups:o}=i,r={nextPart:new Map,validators:[]};for(const h in o)fr(o[h],r,h,s);return r},fr=(i,s,o,r)=>{i.forEach(h=>{if(typeof h=="string"){const m=h===""?s:Ah(s,h);m.classGroupId=o;return}if(typeof h=="function"){if(pv(h)){fr(h(r),s,o,r);return}s.validators.push({validator:h,classGroupId:o});return}Object.entries(h).forEach(([m,S])=>{fr(S,Ah(s,m),o,r)})})},Ah=(i,s)=>{let o=i;return s.split(Sr).forEach(r=>{o.nextPart.has(r)||o.nextPart.set(r,{nextPart:new Map,validators:[]}),o=o.nextPart.get(r)}),o},pv=i=>i.isThemeGetter,yv=i=>{if(i<1)return{get:()=>{},set:()=>{}};let s=0,o=new Map,r=new Map;const h=(m,S)=>{o.set(m,S),s++,s>i&&(s=0,r=o,o=new Map)};return{get(m){let S=o.get(m);if(S!==void 0)return S;if((S=r.get(m))!==void 0)return h(m,S),S},set(m,S){o.has(m)?o.set(m,S):h(m,S)}}},dr="!",hr=":",bv=hr.length,xv=i=>{const{prefix:s,experimentalParseClassName:o}=i;let r=h=>{const m=[];let S=0,M=0,x=0,p;for(let G=0;G<h.length;G++){let H=h[G];if(S===0&&M===0){if(H===hr){m.push(h.slice(x,G)),x=G+bv;continue}if(H==="/"){p=G;continue}}H==="["?S++:H==="]"?S--:H==="("?M++:H===")"&&M--}const N=m.length===0?h:h.substring(x),j=Sv(N),O=j!==N,X=p&&p>x?p-x:void 0;return{modifiers:m,hasImportantModifier:O,baseClassName:j,maybePostfixModifierPosition:X}};if(s){const h=s+hr,m=r;r=S=>S.startsWith(h)?m(S.substring(h.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:S,maybePostfixModifierPosition:void 0}}if(o){const h=r;r=m=>o({className:m,parseClassName:h})}return r},Sv=i=>i.endsWith(dr)?i.substring(0,i.length-1):i.startsWith(dr)?i.substring(1):i,Av=i=>{const s=Object.fromEntries(i.orderSensitiveModifiers.map(r=>[r,!0]));return r=>{if(r.length<=1)return r;const h=[];let m=[];return r.forEach(S=>{S[0]==="["||s[S]?(h.push(...m.sort(),S),m=[]):m.push(S)}),h.push(...m.sort()),h}},Ev=i=>({cache:yv(i.cacheSize),parseClassName:xv(i),sortModifiers:Av(i),...gv(i)}),Mv=/\s+/,Tv=(i,s)=>{const{parseClassName:o,getClassGroupId:r,getConflictingClassGroupIds:h,sortModifiers:m}=s,S=[],M=i.trim().split(Mv);let x="";for(let p=M.length-1;p>=0;p-=1){const N=M[p],{isExternal:j,modifiers:O,hasImportantModifier:X,baseClassName:G,maybePostfixModifierPosition:H}=o(N);if(j){x=N+(x.length>0?" "+x:x);continue}let tt=!!H,ut=r(tt?G.substring(0,H):G);if(!ut){if(!tt){x=N+(x.length>0?" "+x:x);continue}if(ut=r(G),!ut){x=N+(x.length>0?" "+x:x);continue}tt=!1}const nt=m(O).join(":"),dt=X?nt+dr:nt,At=dt+ut;if(S.includes(At))continue;S.push(At);const W=h(ut,tt);for(let B=0;B<W.length;++B){const at=W[B];S.push(dt+at)}x=N+(x.length>0?" "+x:x)}return x};function Dv(){let i=0,s,o,r="";for(;i<arguments.length;)(s=arguments[i++])&&(o=Yh(s))&&(r&&(r+=" "),r+=o);return r}const Yh=i=>{if(typeof i=="string")return i;let s,o="";for(let r=0;r<i.length;r++)i[r]&&(s=Yh(i[r]))&&(o&&(o+=" "),o+=s);return o};function zv(i,...s){let o,r,h,m=S;function S(x){const p=s.reduce((N,j)=>j(N),i());return o=Ev(p),r=o.cache.get,h=o.cache.set,m=M,M(x)}function M(x){const p=r(x);if(p)return p;const N=Tv(x,o);return h(x,N),N}return function(){return m(Dv.apply(null,arguments))}}const ae=i=>{const s=o=>o[i]||[];return s.isThemeGetter=!0,s},Gh=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Lh=/^\((?:(\w[\w-]*):)?(.+)\)$/i,wv=/^\d+\/\d+$/,Rv=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Ov=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Nv=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,_v=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Cv=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,pn=i=>wv.test(i),mt=i=>!!i&&!Number.isNaN(Number(i)),Rl=i=>!!i&&Number.isInteger(Number(i)),Eh=i=>i.endsWith("%")&&mt(i.slice(0,-1)),al=i=>Rv.test(i),Uv=()=>!0,jv=i=>Ov.test(i)&&!Nv.test(i),Ar=()=>!1,Hv=i=>_v.test(i),Bv=i=>Cv.test(i),qv=i=>!V(i)&&!Z(i),Yv=i=>bn(i,Vh,Ar),V=i=>Gh.test(i),Ol=i=>bn(i,Zh,jv),ur=i=>bn(i,Wv,mt),Gv=i=>bn(i,Xh,Ar),Lv=i=>bn(i,Qh,Bv),Xv=i=>bn(i,Ar,Hv),Z=i=>Lh.test(i),Yi=i=>xn(i,Zh),Qv=i=>xn(i,Fv),Vv=i=>xn(i,Xh),Zv=i=>xn(i,Vh),Kv=i=>xn(i,Qh),kv=i=>xn(i,Pv,!0),bn=(i,s,o)=>{const r=Gh.exec(i);return r?r[1]?s(r[1]):o(r[2]):!1},xn=(i,s,o=!1)=>{const r=Lh.exec(i);return r?r[1]?s(r[1]):o:!1},Xh=i=>i==="position",Jv=new Set(["image","url"]),Qh=i=>Jv.has(i),$v=new Set(["length","size","percentage"]),Vh=i=>$v.has(i),Zh=i=>i==="length",Wv=i=>i==="number",Fv=i=>i==="family-name",Pv=i=>i==="shadow",Iv=()=>{const i=ae("color"),s=ae("font"),o=ae("text"),r=ae("font-weight"),h=ae("tracking"),m=ae("leading"),S=ae("breakpoint"),M=ae("container"),x=ae("spacing"),p=ae("radius"),N=ae("shadow"),j=ae("inset-shadow"),O=ae("drop-shadow"),X=ae("blur"),G=ae("perspective"),H=ae("aspect"),tt=ae("ease"),ut=ae("animate"),nt=()=>["auto","avoid","all","avoid-page","page","left","right","column"],dt=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],At=()=>["auto","hidden","clip","visible","scroll"],W=()=>["auto","contain","none"],B=()=>[Z,V,x],at=()=>[pn,"full","auto",...B()],st=()=>[Rl,"none","subgrid",Z,V],Y=()=>["auto",{span:["full",Rl,Z,V]},Z,V],$=()=>[Rl,"auto",Z,V],pt=()=>["auto","min","max","fr",Z,V],_t=()=>["start","end","center","between","around","evenly","stretch","baseline"],zt=()=>["start","end","center","stretch"],Mt=()=>["auto",...B()],Nt=()=>[pn,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...B()],_=()=>[i,Z,V],F=()=>[Eh,Ol],L=()=>["","none","full",p,Z,V],lt=()=>["",mt,Yi,Ol],g=()=>["solid","dashed","dotted","double"],U=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],k=()=>["","none",X,Z,V],K=()=>["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",Z,V],q=()=>["none",mt,Z,V],ct=()=>["none",mt,Z,V],I=()=>[mt,Z,V],Ct=()=>[pn,"full",...B()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[al],breakpoint:[al],color:[Uv],container:[al],"drop-shadow":[al],ease:["in","out","in-out"],font:[qv],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[al],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[al],shadow:[al],spacing:["px",mt],text:[al],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",pn,V,Z,H]}],container:["container"],columns:[{columns:[mt,V,Z,M]}],"break-after":[{"break-after":nt()}],"break-before":[{"break-before":nt()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...dt(),V,Z]}],overflow:[{overflow:At()}],"overflow-x":[{"overflow-x":At()}],"overflow-y":[{"overflow-y":At()}],overscroll:[{overscroll:W()}],"overscroll-x":[{"overscroll-x":W()}],"overscroll-y":[{"overscroll-y":W()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:at()}],"inset-x":[{"inset-x":at()}],"inset-y":[{"inset-y":at()}],start:[{start:at()}],end:[{end:at()}],top:[{top:at()}],right:[{right:at()}],bottom:[{bottom:at()}],left:[{left:at()}],visibility:["visible","invisible","collapse"],z:[{z:[Rl,"auto",Z,V]}],basis:[{basis:[pn,"full","auto",M,...B()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[mt,pn,"auto","initial","none",V]}],grow:[{grow:["",mt,Z,V]}],shrink:[{shrink:["",mt,Z,V]}],order:[{order:[Rl,"first","last","none",Z,V]}],"grid-cols":[{"grid-cols":st()}],"col-start-end":[{col:Y()}],"col-start":[{"col-start":$()}],"col-end":[{"col-end":$()}],"grid-rows":[{"grid-rows":st()}],"row-start-end":[{row:Y()}],"row-start":[{"row-start":$()}],"row-end":[{"row-end":$()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":pt()}],"auto-rows":[{"auto-rows":pt()}],gap:[{gap:B()}],"gap-x":[{"gap-x":B()}],"gap-y":[{"gap-y":B()}],"justify-content":[{justify:[..._t(),"normal"]}],"justify-items":[{"justify-items":[...zt(),"normal"]}],"justify-self":[{"justify-self":["auto",...zt()]}],"align-content":[{content:["normal",..._t()]}],"align-items":[{items:[...zt(),"baseline"]}],"align-self":[{self:["auto",...zt(),"baseline"]}],"place-content":[{"place-content":_t()}],"place-items":[{"place-items":[...zt(),"baseline"]}],"place-self":[{"place-self":["auto",...zt()]}],p:[{p:B()}],px:[{px:B()}],py:[{py:B()}],ps:[{ps:B()}],pe:[{pe:B()}],pt:[{pt:B()}],pr:[{pr:B()}],pb:[{pb:B()}],pl:[{pl:B()}],m:[{m:Mt()}],mx:[{mx:Mt()}],my:[{my:Mt()}],ms:[{ms:Mt()}],me:[{me:Mt()}],mt:[{mt:Mt()}],mr:[{mr:Mt()}],mb:[{mb:Mt()}],ml:[{ml:Mt()}],"space-x":[{"space-x":B()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":B()}],"space-y-reverse":["space-y-reverse"],size:[{size:Nt()}],w:[{w:[M,"screen",...Nt()]}],"min-w":[{"min-w":[M,"screen","none",...Nt()]}],"max-w":[{"max-w":[M,"screen","none","prose",{screen:[S]},...Nt()]}],h:[{h:["screen",...Nt()]}],"min-h":[{"min-h":["screen","none",...Nt()]}],"max-h":[{"max-h":["screen",...Nt()]}],"font-size":[{text:["base",o,Yi,Ol]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,Z,ur]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Eh,V]}],"font-family":[{font:[Qv,V,s]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[h,Z,V]}],"line-clamp":[{"line-clamp":[mt,"none",Z,ur]}],leading:[{leading:[m,...B()]}],"list-image":[{"list-image":["none",Z,V]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",Z,V]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:_()}],"text-color":[{text:_()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...g(),"wavy"]}],"text-decoration-thickness":[{decoration:[mt,"from-font","auto",Z,Ol]}],"text-decoration-color":[{decoration:_()}],"underline-offset":[{"underline-offset":[mt,"auto",Z,V]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:B()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Z,V]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Z,V]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...dt(),Vv,Gv]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","space","round"]}]}],"bg-size":[{bg:["auto","cover","contain",Zv,Yv]}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Rl,Z,V],radial:["",Z,V],conic:[Rl,Z,V]},Kv,Lv]}],"bg-color":[{bg:_()}],"gradient-from-pos":[{from:F()}],"gradient-via-pos":[{via:F()}],"gradient-to-pos":[{to:F()}],"gradient-from":[{from:_()}],"gradient-via":[{via:_()}],"gradient-to":[{to:_()}],rounded:[{rounded:L()}],"rounded-s":[{"rounded-s":L()}],"rounded-e":[{"rounded-e":L()}],"rounded-t":[{"rounded-t":L()}],"rounded-r":[{"rounded-r":L()}],"rounded-b":[{"rounded-b":L()}],"rounded-l":[{"rounded-l":L()}],"rounded-ss":[{"rounded-ss":L()}],"rounded-se":[{"rounded-se":L()}],"rounded-ee":[{"rounded-ee":L()}],"rounded-es":[{"rounded-es":L()}],"rounded-tl":[{"rounded-tl":L()}],"rounded-tr":[{"rounded-tr":L()}],"rounded-br":[{"rounded-br":L()}],"rounded-bl":[{"rounded-bl":L()}],"border-w":[{border:lt()}],"border-w-x":[{"border-x":lt()}],"border-w-y":[{"border-y":lt()}],"border-w-s":[{"border-s":lt()}],"border-w-e":[{"border-e":lt()}],"border-w-t":[{"border-t":lt()}],"border-w-r":[{"border-r":lt()}],"border-w-b":[{"border-b":lt()}],"border-w-l":[{"border-l":lt()}],"divide-x":[{"divide-x":lt()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":lt()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...g(),"hidden","none"]}],"divide-style":[{divide:[...g(),"hidden","none"]}],"border-color":[{border:_()}],"border-color-x":[{"border-x":_()}],"border-color-y":[{"border-y":_()}],"border-color-s":[{"border-s":_()}],"border-color-e":[{"border-e":_()}],"border-color-t":[{"border-t":_()}],"border-color-r":[{"border-r":_()}],"border-color-b":[{"border-b":_()}],"border-color-l":[{"border-l":_()}],"divide-color":[{divide:_()}],"outline-style":[{outline:[...g(),"none","hidden"]}],"outline-offset":[{"outline-offset":[mt,Z,V]}],"outline-w":[{outline:["",mt,Yi,Ol]}],"outline-color":[{outline:[i]}],shadow:[{shadow:["","none",N,kv,Xv]}],"shadow-color":[{shadow:_()}],"inset-shadow":[{"inset-shadow":["none",Z,V,j]}],"inset-shadow-color":[{"inset-shadow":_()}],"ring-w":[{ring:lt()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:_()}],"ring-offset-w":[{"ring-offset":[mt,Ol]}],"ring-offset-color":[{"ring-offset":_()}],"inset-ring-w":[{"inset-ring":lt()}],"inset-ring-color":[{"inset-ring":_()}],opacity:[{opacity:[mt,Z,V]}],"mix-blend":[{"mix-blend":[...U(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":U()}],filter:[{filter:["","none",Z,V]}],blur:[{blur:k()}],brightness:[{brightness:[mt,Z,V]}],contrast:[{contrast:[mt,Z,V]}],"drop-shadow":[{"drop-shadow":["","none",O,Z,V]}],grayscale:[{grayscale:["",mt,Z,V]}],"hue-rotate":[{"hue-rotate":[mt,Z,V]}],invert:[{invert:["",mt,Z,V]}],saturate:[{saturate:[mt,Z,V]}],sepia:[{sepia:["",mt,Z,V]}],"backdrop-filter":[{"backdrop-filter":["","none",Z,V]}],"backdrop-blur":[{"backdrop-blur":k()}],"backdrop-brightness":[{"backdrop-brightness":[mt,Z,V]}],"backdrop-contrast":[{"backdrop-contrast":[mt,Z,V]}],"backdrop-grayscale":[{"backdrop-grayscale":["",mt,Z,V]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[mt,Z,V]}],"backdrop-invert":[{"backdrop-invert":["",mt,Z,V]}],"backdrop-opacity":[{"backdrop-opacity":[mt,Z,V]}],"backdrop-saturate":[{"backdrop-saturate":[mt,Z,V]}],"backdrop-sepia":[{"backdrop-sepia":["",mt,Z,V]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":B()}],"border-spacing-x":[{"border-spacing-x":B()}],"border-spacing-y":[{"border-spacing-y":B()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",Z,V]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[mt,"initial",Z,V]}],ease:[{ease:["linear","initial",tt,Z,V]}],delay:[{delay:[mt,Z,V]}],animate:[{animate:["none",ut,Z,V]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[G,Z,V]}],"perspective-origin":[{"perspective-origin":K()}],rotate:[{rotate:q()}],"rotate-x":[{"rotate-x":q()}],"rotate-y":[{"rotate-y":q()}],"rotate-z":[{"rotate-z":q()}],scale:[{scale:ct()}],"scale-x":[{"scale-x":ct()}],"scale-y":[{"scale-y":ct()}],"scale-z":[{"scale-z":ct()}],"scale-3d":["scale-3d"],skew:[{skew:I()}],"skew-x":[{"skew-x":I()}],"skew-y":[{"skew-y":I()}],transform:[{transform:[Z,V,"","none","gpu","cpu"]}],"transform-origin":[{origin:K()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Ct()}],"translate-x":[{"translate-x":Ct()}],"translate-y":[{"translate-y":Ct()}],"translate-z":[{"translate-z":Ct()}],"translate-none":["translate-none"],accent:[{accent:_()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:_()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Z,V]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":B()}],"scroll-mx":[{"scroll-mx":B()}],"scroll-my":[{"scroll-my":B()}],"scroll-ms":[{"scroll-ms":B()}],"scroll-me":[{"scroll-me":B()}],"scroll-mt":[{"scroll-mt":B()}],"scroll-mr":[{"scroll-mr":B()}],"scroll-mb":[{"scroll-mb":B()}],"scroll-ml":[{"scroll-ml":B()}],"scroll-p":[{"scroll-p":B()}],"scroll-px":[{"scroll-px":B()}],"scroll-py":[{"scroll-py":B()}],"scroll-ps":[{"scroll-ps":B()}],"scroll-pe":[{"scroll-pe":B()}],"scroll-pt":[{"scroll-pt":B()}],"scroll-pr":[{"scroll-pr":B()}],"scroll-pb":[{"scroll-pb":B()}],"scroll-pl":[{"scroll-pl":B()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Z,V]}],fill:[{fill:["none",..._()]}],"stroke-w":[{stroke:[mt,Yi,Ol,ur]}],stroke:[{stroke:["none",..._()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["before","after","placeholder","file","marker","selection","first-line","first-letter","backdrop","*","**"]}},tp=zv(Iv);function ir(...i){return tp(hv(i))}const ep=()=>{const[i,s]=w.useState(""),[o,r]=w.useState("Executive Summary"),[h,m]=w.useState(!1),[S,M]=w.useState(!0),[x,p]=w.useState([{id:"1",content:"Generate 5 attention-grabbing headlines for an article about AI Chat Copywriter",sender:"user",timestamp:new Date,mpid:"c1f357d6-35ec-4b70-af76-7e7f7b6a13aa"},{id:"2",content:`Here's the results of 5 attention-grabbing headlines:

1. "Revolutionize Customer Engagement with AI Chat Copywriter"

2. "Unleash the Power of AI Chat Copywriters for Transformative Customer Experiences"

3. "Chatbots on Steroids: Meet the AI Copywriter Transforming Conversations"

4. "From Bland to Brilliant: AI Chat Copywriters Make Brands Conversational Rockstars"

5. "Say Goodbye to Boring Chats: AI Copywriters Elevate Conversational Marketing"`,sender:"agent",timestamp:new Date,mpid:"92e4920a-2d71-4131-962d-e3cfdc5732c8"}]),N=[{id:"1",title:"Generate 5 attention-grab...",description:'"Revolutionize Customer Enga...',mpid:"2a3c0fa7-da75-45f8-a9b7-a99a5fb4c0c8"},{id:"2",title:"Learning From 100 Years o...",description:"For athletes, high altitude prod...",mpid:"9fe1e28e-1cb3-44ae-a33c-db30f56560a5"},{id:"3",title:"Research officiants",description:"Maxwell's equations—the foun...",mpid:"bf604eab-6d1a-442f-8c9f-0d6f5798face"},{id:"4",title:"What does a senior lead de...",description:"Physiological respiration involv...",mpid:"209dfc8d-74e6-45c7-a5b6-70cafb8837ed"},{id:"5",title:"Write a sweet note to your...",description:"In the eighteenth century the G...",mpid:"1b59d194-276a-4cd9-851d-3d7e8db681ae"},{id:"6",title:"Meet with cake bakers",description:"Physical space is often conceiv...",mpid:"9ec2e4fc-e627-4781-9989-b33af16a2e7b"},{id:"7",title:"Meet with cake bakers",description:"Physical space is often conceiv...",mpid:"e2c4590b-c9f3-4a05-bf2f-d09352bbf5ad"}],j=()=>{if(!i.trim())return;const O={id:Date.now().toString(),content:i,sender:"user",timestamp:new Date};p(X=>[...X,O]),s(""),setTimeout(()=>{const X={id:(Date.now()+1).toString(),content:"I understand your request. Let me help you with that.",sender:"agent",timestamp:new Date};p(G=>[...G,X])},1e3)};return z.jsxs("div",{className:"flex h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30 text-slate-900","data-magicpath-id":"0","data-magicpath-path":"AgentPage.tsx",children:[z.jsxs("aside",{className:ir("bg-white/80 backdrop-blur-xl border-r border-slate-200/60 flex flex-col transition-all duration-300 ease-in-out shadow-xl",S?"w-80":"w-0 overflow-hidden"),"data-magicpath-id":"1","data-magicpath-path":"AgentPage.tsx",children:[z.jsxs("header",{className:"p-6 border-b border-slate-200/60","data-magicpath-id":"2","data-magicpath-path":"AgentPage.tsx",children:[z.jsxs("div",{className:"flex items-center space-x-3 mb-6","data-magicpath-id":"3","data-magicpath-path":"AgentPage.tsx",children:[z.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-indigo-600 to-purple-700 rounded-2xl flex items-center justify-center shadow-lg","data-magicpath-id":"4","data-magicpath-path":"AgentPage.tsx",children:z.jsx(nr,{className:"w-5 h-5 text-white"})}),z.jsxs("div",{"data-magicpath-id":"5","data-magicpath-path":"AgentPage.tsx",children:[z.jsx("h1",{className:"font-bold text-xl bg-gradient-to-r from-indigo-600 to-purple-700 bg-clip-text text-transparent","data-magicpath-id":"6","data-magicpath-path":"AgentPage.tsx",children:"Robynnv3"}),z.jsx("p",{className:"text-xs text-slate-500 font-medium","data-magicpath-id":"7","data-magicpath-path":"AgentPage.tsx",children:"AI Assistant"})]})]}),z.jsxs("div",{className:"relative","data-magicpath-id":"8","data-magicpath-path":"AgentPage.tsx",children:[z.jsx(W0,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400","data-magicpath-id":"9","data-magicpath-path":"AgentPage.tsx"}),z.jsx("input",{type:"text",placeholder:"Search conversations...",className:"w-full pl-12 pr-10 py-3 bg-slate-50/80 border border-slate-200/60 rounded-2xl text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-300 transition-all duration-200 placeholder:text-slate-400","data-magicpath-id":"10","data-magicpath-path":"AgentPage.tsx"}),z.jsx("kbd",{className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-xs text-slate-400 bg-slate-100 px-2 py-1 rounded-md font-mono","data-magicpath-id":"11","data-magicpath-path":"AgentPage.tsx",children:"⌘K"})]})]}),z.jsx("nav",{className:"flex-1 p-6","data-magicpath-id":"12","data-magicpath-path":"AgentPage.tsx",children:z.jsxs("div",{className:"space-y-2","data-magicpath-id":"13","data-magicpath-path":"AgentPage.tsx",children:[z.jsxs("button",{className:"flex items-center w-full px-4 py-3 text-sm text-white bg-gradient-to-r from-indigo-600 to-purple-700 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-200 font-medium","data-magicpath-id":"14","data-magicpath-path":"AgentPage.tsx",children:[z.jsx(X0,{className:"w-4 h-4 mr-3","data-magicpath-id":"15","data-magicpath-path":"AgentPage.tsx"}),"AI Chat"]}),z.jsxs("button",{className:"flex items-center w-full px-4 py-3 text-sm text-slate-600 hover:bg-slate-50/80 hover:text-slate-900 rounded-2xl transition-all duration-200 font-medium","data-magicpath-id":"16","data-magicpath-path":"AgentPage.tsx",children:[z.jsx(ph,{className:"w-4 h-4 mr-3","data-magicpath-id":"17","data-magicpath-path":"AgentPage.tsx"}),"Projects"]}),z.jsxs("button",{className:"flex items-center w-full px-4 py-3 text-sm text-slate-600 hover:bg-slate-50/80 hover:text-slate-900 rounded-2xl transition-all duration-200 font-medium","data-magicpath-id":"18","data-magicpath-path":"AgentPage.tsx",children:[z.jsx(nr,{className:"w-4 h-4 mr-3"}),"Templates"]}),z.jsxs("button",{className:"flex items-center w-full px-4 py-3 text-sm text-slate-600 hover:bg-slate-50/80 hover:text-slate-900 rounded-2xl transition-all duration-200 font-medium","data-magicpath-id":"19","data-magicpath-path":"AgentPage.tsx",children:[z.jsx(ph,{className:"w-4 h-4 mr-3","data-magicpath-id":"20","data-magicpath-path":"AgentPage.tsx"}),"Documents"]}),z.jsxs("button",{className:"flex items-center w-full px-4 py-3 text-sm text-slate-600 hover:bg-slate-50/80 hover:text-slate-900 rounded-2xl transition-all duration-200 font-medium","data-magicpath-id":"21","data-magicpath-path":"AgentPage.tsx",children:[z.jsx(xh,{className:"w-4 h-4 mr-3","data-magicpath-id":"22","data-magicpath-path":"AgentPage.tsx"}),"Community",z.jsx("span",{className:"ml-auto bg-gradient-to-r from-emerald-500 to-teal-600 text-white text-xs px-2 py-1 rounded-full font-semibold shadow-sm","data-magicpath-id":"23","data-magicpath-path":"AgentPage.tsx",children:"NEW"})]}),z.jsxs("button",{className:"flex items-center w-full px-4 py-3 text-sm text-slate-600 hover:bg-slate-50/80 hover:text-slate-900 rounded-2xl transition-all duration-200 font-medium","data-magicpath-id":"24","data-magicpath-path":"AgentPage.tsx",children:[z.jsx(j0,{className:"w-4 h-4 mr-3","data-magicpath-id":"25","data-magicpath-path":"AgentPage.tsx"}),"History"]})]})}),z.jsxs("footer",{className:"p-6 border-t border-slate-200/60","data-magicpath-id":"26","data-magicpath-path":"AgentPage.tsx",children:[z.jsx("div",{className:"space-y-2 mb-6","data-magicpath-id":"27","data-magicpath-path":"AgentPage.tsx",children:z.jsxs("button",{className:"flex items-center w-full px-4 py-3 text-sm text-slate-600 hover:bg-slate-50/80 hover:text-slate-900 rounded-2xl transition-all duration-200 font-medium","data-magicpath-id":"28","data-magicpath-path":"AgentPage.tsx",children:[z.jsx(tv,{className:"w-4 h-4 mr-3","data-magicpath-id":"29","data-magicpath-path":"AgentPage.tsx"}),"Settings"]})}),z.jsxs("div",{className:"flex items-center p-4 bg-slate-50/80 rounded-2xl border border-slate-200/60","data-magicpath-id":"30","data-magicpath-path":"AgentPage.tsx",children:[z.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-rose-400 to-pink-500 rounded-xl mr-3 flex items-center justify-center shadow-sm","data-magicpath-id":"31","data-magicpath-path":"AgentPage.tsx",children:z.jsx("span",{className:"text-white text-sm font-semibold","data-magicpath-id":"32","data-magicpath-path":"AgentPage.tsx",children:"EC"})}),z.jsxs("div",{className:"flex-1 min-w-0","data-magicpath-id":"33","data-magicpath-path":"AgentPage.tsx",children:[z.jsx("p",{className:"text-sm font-semibold text-slate-900 truncate","data-magicpath-id":"34","data-magicpath-path":"AgentPage.tsx",children:"Emilia Caitlin"}),z.jsx("p",{className:"text-xs text-slate-500 truncate","data-magicpath-id":"35","data-magicpath-path":"AgentPage.tsx",children:"<EMAIL>"})]})]})]})]}),z.jsxs("main",{className:"flex-1 flex flex-col","data-magicpath-id":"36","data-magicpath-path":"AgentPage.tsx",children:[z.jsxs("header",{className:"flex items-center justify-between p-6 border-b border-slate-200/60 bg-white/60 backdrop-blur-xl","data-magicpath-id":"37","data-magicpath-path":"AgentPage.tsx",children:[z.jsxs("div",{className:"flex items-center space-x-4","data-magicpath-id":"38","data-magicpath-path":"AgentPage.tsx",children:[z.jsx("button",{onClick:()=>M(!S),className:"p-2 hover:bg-slate-100/80 rounded-xl transition-colors duration-200","data-magicpath-id":"39","data-magicpath-path":"AgentPage.tsx",children:S?z.jsx(ov,{className:"w-5 h-5 text-slate-600","data-magicpath-id":"40","data-magicpath-path":"AgentPage.tsx"}):z.jsx(G0,{className:"w-5 h-5 text-slate-600","data-magicpath-id":"41","data-magicpath-path":"AgentPage.tsx"})}),z.jsxs("div",{className:"flex items-center space-x-3","data-magicpath-id":"42","data-magicpath-path":"AgentPage.tsx",children:[z.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-indigo-600 to-purple-700 rounded-xl flex items-center justify-center","data-magicpath-id":"43","data-magicpath-path":"AgentPage.tsx",children:z.jsx(vh,{className:"w-4 h-4 text-white","data-magicpath-id":"44","data-magicpath-path":"AgentPage.tsx"})}),z.jsxs("div",{"data-magicpath-id":"45","data-magicpath-path":"AgentPage.tsx",children:[z.jsx("h1",{className:"text-xl font-bold text-slate-900","data-magicpath-id":"46","data-magicpath-path":"AgentPage.tsx",children:"SEO Agent"}),z.jsx("p",{className:"text-xs text-slate-500 font-medium","data-magicpath-id":"47","data-magicpath-path":"AgentPage.tsx",children:"Powered by Robynnv3"})]})]})]}),z.jsx("div",{className:"flex items-center space-x-3","data-magicpath-id":"48","data-magicpath-path":"AgentPage.tsx",children:z.jsxs("button",{className:"bg-gradient-to-r from-indigo-600 to-purple-700 text-white px-6 py-2.5 rounded-2xl text-sm font-semibold flex items-center shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105","data-magicpath-id":"49","data-magicpath-path":"AgentPage.tsx",children:[z.jsx(dv,{className:"w-4 h-4 mr-2","data-magicpath-id":"50","data-magicpath-path":"AgentPage.tsx"}),"Upgrade Pro"]})})]}),z.jsxs("div",{className:"flex flex-1","data-magicpath-id":"51","data-magicpath-path":"AgentPage.tsx",children:[z.jsxs("div",{className:"flex-1 flex flex-col","data-magicpath-id":"52","data-magicpath-path":"AgentPage.tsx",children:[z.jsxs("div",{className:"flex-1 overflow-y-auto p-8","data-magicpath-id":"53","data-magicpath-path":"AgentPage.tsx",children:[z.jsx("div",{className:"max-w-4xl mx-auto space-y-8","data-magicpath-id":"54","data-magicpath-path":"AgentPage.tsx",children:x.map(O=>z.jsxs("div",{className:"flex items-start space-x-4","data-magicpath-uuid":O.mpid??"unsafe","data-magicpath-id":"55","data-magicpath-path":"AgentPage.tsx",children:[z.jsx("div",{className:ir("w-10 h-10 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-sm",O.sender==="user"?"bg-gradient-to-br from-slate-600 to-slate-700":"bg-gradient-to-br from-indigo-600 to-purple-700"),"data-magicpath-uuid":O.mpid??"unsafe","data-magicpath-id":"56","data-magicpath-path":"AgentPage.tsx",children:O.sender==="user"?z.jsx(xh,{className:"w-5 h-5 text-white","data-magicpath-uuid":O.mpid??"unsafe","data-magicpath-id":"57","data-magicpath-path":"AgentPage.tsx"}):z.jsx(vh,{className:"w-5 h-5 text-white","data-magicpath-uuid":O.mpid??"unsafe","data-magicpath-id":"58","data-magicpath-path":"AgentPage.tsx"})}),z.jsxs("div",{className:"flex-1","data-magicpath-uuid":O.mpid??"unsafe","data-magicpath-id":"59","data-magicpath-path":"AgentPage.tsx",children:[z.jsx("div",{className:ir("p-6 rounded-3xl shadow-sm border",O.sender==="user"?"bg-slate-50/80 border-slate-200/60":"bg-white/80 border-slate-200/60"),"data-magicpath-uuid":O.mpid??"unsafe","data-magicpath-id":"60","data-magicpath-path":"AgentPage.tsx",children:z.jsx("p",{className:"text-slate-900 whitespace-pre-wrap leading-relaxed","data-magicpath-uuid":O.mpid??"unsafe","data-magicpath-field":"content:unknown","data-magicpath-id":"61","data-magicpath-path":"AgentPage.tsx",children:O.content})}),O.sender==="agent"&&z.jsxs("div",{className:"flex items-center space-x-2 mt-4 ml-2","data-magicpath-uuid":O.mpid??"unsafe","data-magicpath-id":"62","data-magicpath-path":"AgentPage.tsx",children:[z.jsx("button",{className:"p-2 hover:bg-slate-100/80 rounded-xl transition-colors duration-200","data-magicpath-uuid":O.mpid??"unsafe","data-magicpath-id":"63","data-magicpath-path":"AgentPage.tsx",children:z.jsx(cv,{className:"w-4 h-4 text-slate-500","data-magicpath-uuid":O.mpid??"unsafe","data-magicpath-id":"64","data-magicpath-path":"AgentPage.tsx"})}),z.jsx("button",{className:"p-2 hover:bg-slate-100/80 rounded-xl transition-colors duration-200","data-magicpath-uuid":O.mpid??"unsafe","data-magicpath-id":"65","data-magicpath-path":"AgentPage.tsx",children:z.jsx(uv,{className:"w-4 h-4 text-slate-500","data-magicpath-uuid":O.mpid??"unsafe","data-magicpath-id":"66","data-magicpath-path":"AgentPage.tsx"})}),z.jsxs("button",{className:"flex items-center px-4 py-2 text-sm text-slate-600 hover:bg-slate-100/80 rounded-xl transition-colors duration-200 font-medium","data-magicpath-uuid":O.mpid??"unsafe","data-magicpath-id":"67","data-magicpath-path":"AgentPage.tsx",children:[z.jsx(B0,{className:"w-4 h-4 mr-2","data-magicpath-uuid":O.mpid??"unsafe","data-magicpath-id":"68","data-magicpath-path":"AgentPage.tsx"}),"Copy"]}),z.jsxs("button",{className:"flex items-center px-4 py-2 text-sm text-slate-600 hover:bg-slate-100/80 rounded-xl transition-colors duration-200 font-medium","data-magicpath-uuid":O.mpid??"unsafe","data-magicpath-id":"69","data-magicpath-path":"AgentPage.tsx",children:[z.jsx(av,{className:"w-4 h-4 mr-2","data-magicpath-uuid":O.mpid??"unsafe","data-magicpath-id":"70","data-magicpath-path":"AgentPage.tsx"}),"Share"]})]})]})]},O.id))}),z.jsx("div",{className:"max-w-4xl mx-auto mt-8","data-magicpath-id":"71","data-magicpath-path":"AgentPage.tsx",children:z.jsxs("button",{className:"flex items-center px-6 py-3 text-sm text-slate-600 hover:bg-slate-100/80 rounded-2xl border border-slate-200/60 transition-all duration-200 font-medium hover:shadow-sm","data-magicpath-id":"72","data-magicpath-path":"AgentPage.tsx",children:[z.jsx(J0,{className:"w-4 h-4 mr-2","data-magicpath-id":"73","data-magicpath-path":"AgentPage.tsx"}),"Regenerate Response"]})})]}),z.jsx("div",{className:"p-8 border-t border-slate-200/60 bg-white/60 backdrop-blur-xl","data-magicpath-id":"74","data-magicpath-path":"AgentPage.tsx",children:z.jsxs("div",{className:"max-w-4xl mx-auto","data-magicpath-id":"75","data-magicpath-path":"AgentPage.tsx",children:[z.jsxs("div",{className:"relative","data-magicpath-id":"76","data-magicpath-path":"AgentPage.tsx",children:[z.jsx("textarea",{value:i,onChange:O=>s(O.target.value),onKeyDown:O=>{O.key==="Enter"&&!O.shiftKey&&(O.preventDefault(),j())},placeholder:"Ask me anything...",className:"w-full px-6 py-4 pr-32 border border-slate-200/60 rounded-3xl resize-none focus:outline-none focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-300 bg-white/80 backdrop-blur-sm shadow-sm transition-all duration-200 placeholder:text-slate-400",rows:1,style:{minHeight:"56px",maxHeight:"140px"},"data-magicpath-id":"77","data-magicpath-path":"AgentPage.tsx"}),z.jsxs("div",{className:"absolute right-3 bottom-3 flex items-center space-x-2","data-magicpath-id":"78","data-magicpath-path":"AgentPage.tsx",children:[z.jsx("button",{className:"p-2.5 hover:bg-slate-100/80 rounded-xl transition-colors duration-200","data-magicpath-id":"79","data-magicpath-path":"AgentPage.tsx",children:z.jsx(bh,{className:"w-4 h-4 text-slate-500","data-magicpath-id":"80","data-magicpath-path":"AgentPage.tsx"})}),z.jsx("button",{className:"p-2.5 hover:bg-slate-100/80 rounded-xl transition-colors duration-200","data-magicpath-id":"81","data-magicpath-path":"AgentPage.tsx",children:z.jsx(yh,{className:"w-4 h-4 text-slate-500","data-magicpath-id":"82","data-magicpath-path":"AgentPage.tsx"})}),z.jsx("button",{onClick:j,disabled:!i.trim(),className:"p-2.5 bg-gradient-to-r from-indigo-600 to-purple-700 text-white rounded-xl hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 hover:scale-105","data-magicpath-id":"83","data-magicpath-path":"AgentPage.tsx",children:z.jsx(P0,{className:"w-4 h-4","data-magicpath-id":"84","data-magicpath-path":"AgentPage.tsx"})})]})]}),z.jsxs("div",{className:"flex items-center justify-between mt-4","data-magicpath-id":"85","data-magicpath-path":"AgentPage.tsx",children:[z.jsxs("div",{className:"flex items-center space-x-3","data-magicpath-id":"86","data-magicpath-path":"AgentPage.tsx",children:[z.jsxs("button",{className:"flex items-center px-4 py-2.5 text-sm text-slate-600 hover:bg-slate-100/80 rounded-2xl border border-slate-200/60 transition-all duration-200 font-medium hover:shadow-sm","data-magicpath-id":"87","data-magicpath-path":"AgentPage.tsx",children:[z.jsx(bh,{className:"w-4 h-4 mr-2","data-magicpath-id":"88","data-magicpath-path":"AgentPage.tsx"}),"Attach File"]}),z.jsxs("button",{className:"flex items-center px-4 py-2.5 text-sm text-slate-600 hover:bg-slate-100/80 rounded-2xl border border-slate-200/60 transition-all duration-200 font-medium hover:shadow-sm","data-magicpath-id":"89","data-magicpath-path":"AgentPage.tsx",children:[z.jsx(yh,{className:"w-4 h-4 mr-2","data-magicpath-id":"90","data-magicpath-path":"AgentPage.tsx"}),"Voice Message"]}),z.jsxs("button",{className:"flex items-center px-4 py-2.5 text-sm text-white bg-gradient-to-r from-indigo-600 to-purple-700 hover:shadow-lg rounded-2xl transition-all duration-200 font-medium hover:scale-105","data-magicpath-id":"91","data-magicpath-path":"AgentPage.tsx",children:[z.jsx(nr,{className:"w-4 h-4 mr-2"}),"Browse Prompts"]})]}),z.jsxs("div",{className:"text-xs text-slate-400 font-medium","data-magicpath-id":"92","data-magicpath-path":"AgentPage.tsx",children:[i.length," / 3,000"]})]}),z.jsx("p",{className:"text-xs text-slate-400 text-center mt-6 font-medium","data-magicpath-id":"93","data-magicpath-path":"AgentPage.tsx",children:"Robynnv3 may generate inaccurate information. Please verify important details. Model: Robynnv3 Advanced"})]})})]}),z.jsxs("aside",{className:"w-96 bg-white/60 backdrop-blur-xl border-l border-slate-200/60 p-8","data-magicpath-id":"94","data-magicpath-path":"AgentPage.tsx",children:[z.jsxs("div",{className:"flex items-center justify-between mb-8","data-magicpath-id":"95","data-magicpath-path":"AgentPage.tsx",children:[z.jsxs("div",{"data-magicpath-id":"96","data-magicpath-path":"AgentPage.tsx",children:[z.jsx("h2",{className:"text-lg font-bold text-slate-900","data-magicpath-id":"97","data-magicpath-path":"AgentPage.tsx",children:"Recent Projects"}),z.jsxs("p",{className:"text-sm text-slate-500 font-medium","data-magicpath-id":"98","data-magicpath-path":"AgentPage.tsx",children:[N.length," conversations"]})]}),z.jsx("button",{className:"p-2.5 text-slate-400 hover:text-slate-600 hover:bg-slate-100/80 rounded-xl transition-colors duration-200","data-magicpath-id":"99","data-magicpath-path":"AgentPage.tsx",children:z.jsx(K0,{className:"w-5 h-5","data-magicpath-id":"100","data-magicpath-path":"AgentPage.tsx"})})]}),z.jsx("div",{className:"space-y-4","data-magicpath-id":"101","data-magicpath-path":"AgentPage.tsx",children:N.map(O=>z.jsxs("div",{className:"p-5 border border-slate-200/60 rounded-3xl hover:bg-slate-50/80 cursor-pointer transition-all duration-200 hover:shadow-sm hover:scale-[1.02] bg-white/40 backdrop-blur-sm","data-magicpath-uuid":O.mpid??"unsafe","data-magicpath-id":"102","data-magicpath-path":"AgentPage.tsx",children:[z.jsx("h3",{className:"font-semibold text-sm mb-2 text-slate-900 line-clamp-2","data-magicpath-uuid":O.mpid??"unsafe","data-magicpath-field":"title:string","data-magicpath-id":"103","data-magicpath-path":"AgentPage.tsx",children:O.title}),z.jsx("p",{className:"text-xs text-slate-500 line-clamp-3 leading-relaxed","data-magicpath-uuid":O.mpid??"unsafe","data-magicpath-field":"description:string","data-magicpath-id":"104","data-magicpath-path":"AgentPage.tsx",children:O.description}),z.jsxs("div",{className:"flex items-center justify-between mt-4","data-magicpath-uuid":O.mpid??"unsafe","data-magicpath-id":"105","data-magicpath-path":"AgentPage.tsx",children:[z.jsx("span",{className:"text-xs text-slate-400 font-medium","data-magicpath-uuid":O.mpid??"unsafe","data-magicpath-id":"106","data-magicpath-path":"AgentPage.tsx",children:"2 hours ago"}),z.jsxs("div",{className:"flex items-center space-x-1","data-magicpath-uuid":O.mpid??"unsafe","data-magicpath-id":"107","data-magicpath-path":"AgentPage.tsx",children:[z.jsx("div",{className:"w-1.5 h-1.5 bg-emerald-500 rounded-full","data-magicpath-uuid":O.mpid??"unsafe","data-magicpath-id":"108","data-magicpath-path":"AgentPage.tsx"}),z.jsx("span",{className:"text-xs text-emerald-600 font-medium","data-magicpath-uuid":O.mpid??"unsafe","data-magicpath-id":"109","data-magicpath-path":"AgentPage.tsx",children:"Active"})]})]})]},O.id))})]})]})]})]})};function ap(){function i(o){document.documentElement.classList.remove("dark")}return i(),w.useMemo(()=>z.jsx(ep,{}),[])}var xu=jh();const Vi=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";function Sn(i){const s=Object.prototype.toString.call(i);return s==="[object Window]"||s==="[object global]"}function Er(i){return"nodeType"in i}function Ae(i){var s,o;return i?Sn(i)?i:Er(i)&&(s=(o=i.ownerDocument)==null?void 0:o.defaultView)!=null?s:window:window}function Mr(i){const{Document:s}=Ae(i);return i instanceof s}function Tu(i){return Sn(i)?!1:i instanceof Ae(i).HTMLElement}function Kh(i){return i instanceof Ae(i).SVGElement}function An(i){return i?Sn(i)?i.document:Er(i)?Mr(i)?i:Tu(i)||Kh(i)?i.ownerDocument:document:document:document}const Nl=Vi?w.useLayoutEffect:w.useEffect;function Tr(i){const s=w.useRef(i);return Nl(()=>{s.current=i}),w.useCallback(function(){for(var o=arguments.length,r=new Array(o),h=0;h<o;h++)r[h]=arguments[h];return s.current==null?void 0:s.current(...r)},[])}function lp(){const i=w.useRef(null),s=w.useCallback((r,h)=>{i.current=setInterval(r,h)},[]),o=w.useCallback(()=>{i.current!==null&&(clearInterval(i.current),i.current=null)},[]);return[s,o]}function kh(i,s){s===void 0&&(s=[i]);const o=w.useRef(i);return Nl(()=>{o.current!==i&&(o.current=i)},s),o}function Du(i,s){const o=w.useRef();return w.useMemo(()=>{const r=i(o.current);return o.current=r,r},[...s])}function np(i){const s=Tr(i),o=w.useRef(null),r=w.useCallback(h=>{h!==o.current&&(s==null||s(h,o.current)),o.current=h},[]);return[o,r]}function gr(i){const s=w.useRef();return w.useEffect(()=>{s.current=i},[i]),s.current}let cr={};function Jh(i,s){return w.useMemo(()=>{if(s)return s;const o=cr[i]==null?0:cr[i]+1;return cr[i]=o,i+"-"+o},[i,s])}function $h(i){return function(s){for(var o=arguments.length,r=new Array(o>1?o-1:0),h=1;h<o;h++)r[h-1]=arguments[h];return r.reduce((m,S)=>{const M=Object.entries(S);for(const[x,p]of M){const N=m[x];N!=null&&(m[x]=N+i*p)}return m},{...s})}}const yn=$h(1),Li=$h(-1);function up(i){return"clientX"in i&&"clientY"in i}function Wh(i){if(!i)return!1;const{KeyboardEvent:s}=Ae(i.target);return s&&i instanceof s}function ip(i){if(!i)return!1;const{TouchEvent:s}=Ae(i.target);return s&&i instanceof s}function mr(i){if(ip(i)){if(i.touches&&i.touches.length){const{clientX:s,clientY:o}=i.touches[0];return{x:s,y:o}}else if(i.changedTouches&&i.changedTouches.length){const{clientX:s,clientY:o}=i.changedTouches[0];return{x:s,y:o}}}return up(i)?{x:i.clientX,y:i.clientY}:null}const Mh="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function cp(i){return i.matches(Mh)?i:i.querySelector(Mh)}const sp={display:"none"};function rp(i){let{id:s,value:o}=i;return $e.createElement("div",{id:s,style:sp},o)}function op(i){let{id:s,announcement:o,ariaLiveType:r="assertive"}=i;const h={position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"};return $e.createElement("div",{id:s,style:h,role:"status","aria-live":r,"aria-atomic":!0},o)}function fp(){const[i,s]=w.useState("");return{announce:w.useCallback(r=>{r!=null&&s(r)},[]),announcement:i}}const Fh=w.createContext(null);function dp(i){const s=w.useContext(Fh);w.useEffect(()=>{if(!s)throw new Error("useDndMonitor must be used within a children of <DndContext>");return s(i)},[i,s])}function hp(){const[i]=w.useState(()=>new Set),s=w.useCallback(r=>(i.add(r),()=>i.delete(r)),[i]);return[w.useCallback(r=>{let{type:h,event:m}=r;i.forEach(S=>{var M;return(M=S[h])==null?void 0:M.call(S,m)})},[i]),s]}const gp={draggable:`
    To pick up a draggable item, press the space bar.
    While dragging, use the arrow keys to move the item.
    Press space again to drop the item in its new position, or press escape to cancel.
  `},mp={onDragStart(i){let{active:s}=i;return"Picked up draggable item "+s.id+"."},onDragOver(i){let{active:s,over:o}=i;return o?"Draggable item "+s.id+" was moved over droppable area "+o.id+".":"Draggable item "+s.id+" is no longer over a droppable area."},onDragEnd(i){let{active:s,over:o}=i;return o?"Draggable item "+s.id+" was dropped over droppable area "+o.id:"Draggable item "+s.id+" was dropped."},onDragCancel(i){let{active:s}=i;return"Dragging was cancelled. Draggable item "+s.id+" was dropped."}};function vp(i){let{announcements:s=mp,container:o,hiddenTextDescribedById:r,screenReaderInstructions:h=gp}=i;const{announce:m,announcement:S}=fp(),M=Jh("DndLiveRegion"),[x,p]=w.useState(!1);if(w.useEffect(()=>{p(!0)},[]),dp(w.useMemo(()=>({onDragStart(j){let{active:O}=j;m(s.onDragStart({active:O}))},onDragMove(j){let{active:O,over:X}=j;s.onDragMove&&m(s.onDragMove({active:O,over:X}))},onDragOver(j){let{active:O,over:X}=j;m(s.onDragOver({active:O,over:X}))},onDragEnd(j){let{active:O,over:X}=j;m(s.onDragEnd({active:O,over:X}))},onDragCancel(j){let{active:O,over:X}=j;m(s.onDragCancel({active:O,over:X}))}}),[m,s])),!x)return null;const N=$e.createElement($e.Fragment,null,$e.createElement(rp,{id:r,value:h.draggable}),$e.createElement(op,{id:M,announcement:S}));return o?xu.createPortal(N,o):N}var oe;(function(i){i.DragStart="dragStart",i.DragMove="dragMove",i.DragEnd="dragEnd",i.DragCancel="dragCancel",i.DragOver="dragOver",i.RegisterDroppable="registerDroppable",i.SetDroppableDisabled="setDroppableDisabled",i.UnregisterDroppable="unregisterDroppable"})(oe||(oe={}));function Xi(){}const la=Object.freeze({x:0,y:0});function pp(i,s){let{data:{value:o}}=i,{data:{value:r}}=s;return r-o}function yp(i,s){if(!i||i.length===0)return null;const[o]=i;return o[s]}function bp(i,s){const o=Math.max(s.top,i.top),r=Math.max(s.left,i.left),h=Math.min(s.left+s.width,i.left+i.width),m=Math.min(s.top+s.height,i.top+i.height),S=h-r,M=m-o;if(r<h&&o<m){const x=s.width*s.height,p=i.width*i.height,N=S*M,j=N/(x+p-N);return Number(j.toFixed(4))}return 0}const xp=i=>{let{collisionRect:s,droppableRects:o,droppableContainers:r}=i;const h=[];for(const m of r){const{id:S}=m,M=o.get(S);if(M){const x=bp(M,s);x>0&&h.push({id:S,data:{droppableContainer:m,value:x}})}}return h.sort(pp)};function Sp(i,s,o){return{...i,scaleX:s&&o?s.width/o.width:1,scaleY:s&&o?s.height/o.height:1}}function Ph(i,s){return i&&s?{x:i.left-s.left,y:i.top-s.top}:la}function Ap(i){return function(o){for(var r=arguments.length,h=new Array(r>1?r-1:0),m=1;m<r;m++)h[m-1]=arguments[m];return h.reduce((S,M)=>({...S,top:S.top+i*M.y,bottom:S.bottom+i*M.y,left:S.left+i*M.x,right:S.right+i*M.x}),{...o})}}const Ep=Ap(1);function Mp(i){if(i.startsWith("matrix3d(")){const s=i.slice(9,-1).split(/, /);return{x:+s[12],y:+s[13],scaleX:+s[0],scaleY:+s[5]}}else if(i.startsWith("matrix(")){const s=i.slice(7,-1).split(/, /);return{x:+s[4],y:+s[5],scaleX:+s[0],scaleY:+s[3]}}return null}function Tp(i,s,o){const r=Mp(s);if(!r)return i;const{scaleX:h,scaleY:m,x:S,y:M}=r,x=i.left-S-(1-h)*parseFloat(o),p=i.top-M-(1-m)*parseFloat(o.slice(o.indexOf(" ")+1)),N=h?i.width/h:i.width,j=m?i.height/m:i.height;return{width:N,height:j,top:p,right:x+N,bottom:p+j,left:x}}const Dp={ignoreTransform:!1};function zu(i,s){s===void 0&&(s=Dp);let o=i.getBoundingClientRect();if(s.ignoreTransform){const{transform:p,transformOrigin:N}=Ae(i).getComputedStyle(i);p&&(o=Tp(o,p,N))}const{top:r,left:h,width:m,height:S,bottom:M,right:x}=o;return{top:r,left:h,width:m,height:S,bottom:M,right:x}}function Th(i){return zu(i,{ignoreTransform:!0})}function zp(i){const s=i.innerWidth,o=i.innerHeight;return{top:0,left:0,right:s,bottom:o,width:s,height:o}}function wp(i,s){return s===void 0&&(s=Ae(i).getComputedStyle(i)),s.position==="fixed"}function Rp(i,s){s===void 0&&(s=Ae(i).getComputedStyle(i));const o=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(h=>{const m=s[h];return typeof m=="string"?o.test(m):!1})}function Dr(i,s){const o=[];function r(h){if(s!=null&&o.length>=s||!h)return o;if(Mr(h)&&h.scrollingElement!=null&&!o.includes(h.scrollingElement))return o.push(h.scrollingElement),o;if(!Tu(h)||Kh(h)||o.includes(h))return o;const m=Ae(i).getComputedStyle(h);return h!==i&&Rp(h,m)&&o.push(h),wp(h,m)?o:r(h.parentNode)}return i?r(i):o}function Ih(i){const[s]=Dr(i,1);return s??null}function sr(i){return!Vi||!i?null:Sn(i)?i:Er(i)?Mr(i)||i===An(i).scrollingElement?window:Tu(i)?i:null:null}function tg(i){return Sn(i)?i.scrollX:i.scrollLeft}function eg(i){return Sn(i)?i.scrollY:i.scrollTop}function vr(i){return{x:tg(i),y:eg(i)}}var le;(function(i){i[i.Forward=1]="Forward",i[i.Backward=-1]="Backward"})(le||(le={}));function ag(i){return!Vi||!i?!1:i===document.scrollingElement}function lg(i){const s={x:0,y:0},o=ag(i)?{height:window.innerHeight,width:window.innerWidth}:{height:i.clientHeight,width:i.clientWidth},r={x:i.scrollWidth-o.width,y:i.scrollHeight-o.height},h=i.scrollTop<=s.y,m=i.scrollLeft<=s.x,S=i.scrollTop>=r.y,M=i.scrollLeft>=r.x;return{isTop:h,isLeft:m,isBottom:S,isRight:M,maxScroll:r,minScroll:s}}const Op={x:.2,y:.2};function Np(i,s,o,r,h){let{top:m,left:S,right:M,bottom:x}=o;r===void 0&&(r=10),h===void 0&&(h=Op);const{isTop:p,isBottom:N,isLeft:j,isRight:O}=lg(i),X={x:0,y:0},G={x:0,y:0},H={height:s.height*h.y,width:s.width*h.x};return!p&&m<=s.top+H.height?(X.y=le.Backward,G.y=r*Math.abs((s.top+H.height-m)/H.height)):!N&&x>=s.bottom-H.height&&(X.y=le.Forward,G.y=r*Math.abs((s.bottom-H.height-x)/H.height)),!O&&M>=s.right-H.width?(X.x=le.Forward,G.x=r*Math.abs((s.right-H.width-M)/H.width)):!j&&S<=s.left+H.width&&(X.x=le.Backward,G.x=r*Math.abs((s.left+H.width-S)/H.width)),{direction:X,speed:G}}function _p(i){if(i===document.scrollingElement){const{innerWidth:m,innerHeight:S}=window;return{top:0,left:0,right:m,bottom:S,width:m,height:S}}const{top:s,left:o,right:r,bottom:h}=i.getBoundingClientRect();return{top:s,left:o,right:r,bottom:h,width:i.clientWidth,height:i.clientHeight}}function ng(i){return i.reduce((s,o)=>yn(s,vr(o)),la)}function Cp(i){return i.reduce((s,o)=>s+tg(o),0)}function Up(i){return i.reduce((s,o)=>s+eg(o),0)}function jp(i,s){if(s===void 0&&(s=zu),!i)return;const{top:o,left:r,bottom:h,right:m}=s(i);Ih(i)&&(h<=0||m<=0||o>=window.innerHeight||r>=window.innerWidth)&&i.scrollIntoView({block:"center",inline:"center"})}const Hp=[["x",["left","right"],Cp],["y",["top","bottom"],Up]];class zr{constructor(s,o){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;const r=Dr(o),h=ng(r);this.rect={...s},this.width=s.width,this.height=s.height;for(const[m,S,M]of Hp)for(const x of S)Object.defineProperty(this,x,{get:()=>{const p=M(r),N=h[m]-p;return this.rect[x]+N},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class Su{constructor(s){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(o=>{var r;return(r=this.target)==null?void 0:r.removeEventListener(...o)})},this.target=s}add(s,o,r){var h;(h=this.target)==null||h.addEventListener(s,o,r),this.listeners.push([s,o,r])}}function Bp(i){const{EventTarget:s}=Ae(i);return i instanceof s?i:An(i)}function rr(i,s){const o=Math.abs(i.x),r=Math.abs(i.y);return typeof s=="number"?Math.sqrt(o**2+r**2)>s:"x"in s&&"y"in s?o>s.x&&r>s.y:"x"in s?o>s.x:"y"in s?r>s.y:!1}var Je;(function(i){i.Click="click",i.DragStart="dragstart",i.Keydown="keydown",i.ContextMenu="contextmenu",i.Resize="resize",i.SelectionChange="selectionchange",i.VisibilityChange="visibilitychange"})(Je||(Je={}));function Dh(i){i.preventDefault()}function qp(i){i.stopPropagation()}var Et;(function(i){i.Space="Space",i.Down="ArrowDown",i.Right="ArrowRight",i.Left="ArrowLeft",i.Up="ArrowUp",i.Esc="Escape",i.Enter="Enter",i.Tab="Tab"})(Et||(Et={}));const ug={start:[Et.Space,Et.Enter],cancel:[Et.Esc],end:[Et.Space,Et.Enter,Et.Tab]},Yp=(i,s)=>{let{currentCoordinates:o}=s;switch(i.code){case Et.Right:return{...o,x:o.x+25};case Et.Left:return{...o,x:o.x-25};case Et.Down:return{...o,y:o.y+25};case Et.Up:return{...o,y:o.y-25}}};class ig{constructor(s){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=s;const{event:{target:o}}=s;this.props=s,this.listeners=new Su(An(o)),this.windowListeners=new Su(Ae(o)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(Je.Resize,this.handleCancel),this.windowListeners.add(Je.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(Je.Keydown,this.handleKeyDown))}handleStart(){const{activeNode:s,onStart:o}=this.props,r=s.node.current;r&&jp(r),o(la)}handleKeyDown(s){if(Wh(s)){const{active:o,context:r,options:h}=this.props,{keyboardCodes:m=ug,coordinateGetter:S=Yp,scrollBehavior:M="smooth"}=h,{code:x}=s;if(m.end.includes(x)){this.handleEnd(s);return}if(m.cancel.includes(x)){this.handleCancel(s);return}const{collisionRect:p}=r.current,N=p?{x:p.left,y:p.top}:la;this.referenceCoordinates||(this.referenceCoordinates=N);const j=S(s,{active:o,context:r.current,currentCoordinates:N});if(j){const O=Li(j,N),X={x:0,y:0},{scrollableAncestors:G}=r.current;for(const H of G){const tt=s.code,{isTop:ut,isRight:nt,isLeft:dt,isBottom:At,maxScroll:W,minScroll:B}=lg(H),at=_p(H),st={x:Math.min(tt===Et.Right?at.right-at.width/2:at.right,Math.max(tt===Et.Right?at.left:at.left+at.width/2,j.x)),y:Math.min(tt===Et.Down?at.bottom-at.height/2:at.bottom,Math.max(tt===Et.Down?at.top:at.top+at.height/2,j.y))},Y=tt===Et.Right&&!nt||tt===Et.Left&&!dt,$=tt===Et.Down&&!At||tt===Et.Up&&!ut;if(Y&&st.x!==j.x){const pt=H.scrollLeft+O.x,_t=tt===Et.Right&&pt<=W.x||tt===Et.Left&&pt>=B.x;if(_t&&!O.y){H.scrollTo({left:pt,behavior:M});return}_t?X.x=H.scrollLeft-pt:X.x=tt===Et.Right?H.scrollLeft-W.x:H.scrollLeft-B.x,X.x&&H.scrollBy({left:-X.x,behavior:M});break}else if($&&st.y!==j.y){const pt=H.scrollTop+O.y,_t=tt===Et.Down&&pt<=W.y||tt===Et.Up&&pt>=B.y;if(_t&&!O.x){H.scrollTo({top:pt,behavior:M});return}_t?X.y=H.scrollTop-pt:X.y=tt===Et.Down?H.scrollTop-W.y:H.scrollTop-B.y,X.y&&H.scrollBy({top:-X.y,behavior:M});break}}this.handleMove(s,yn(Li(j,this.referenceCoordinates),X))}}}handleMove(s,o){const{onMove:r}=this.props;s.preventDefault(),r(o)}handleEnd(s){const{onEnd:o}=this.props;s.preventDefault(),this.detach(),o()}handleCancel(s){const{onCancel:o}=this.props;s.preventDefault(),this.detach(),o()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}ig.activators=[{eventName:"onKeyDown",handler:(i,s,o)=>{let{keyboardCodes:r=ug,onActivation:h}=s,{active:m}=o;const{code:S}=i.nativeEvent;if(r.start.includes(S)){const M=m.activatorNode.current;return M&&i.target!==M?!1:(i.preventDefault(),h==null||h({event:i.nativeEvent}),!0)}return!1}}];function zh(i){return!!(i&&"distance"in i)}function wh(i){return!!(i&&"delay"in i)}class wr{constructor(s,o,r){var h;r===void 0&&(r=Bp(s.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=s,this.events=o;const{event:m}=s,{target:S}=m;this.props=s,this.events=o,this.document=An(S),this.documentListeners=new Su(this.document),this.listeners=new Su(r),this.windowListeners=new Su(Ae(S)),this.initialCoordinates=(h=mr(m))!=null?h:la,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:s,props:{options:{activationConstraint:o,bypassActivationConstraint:r}}}=this;if(this.listeners.add(s.move.name,this.handleMove,{passive:!1}),this.listeners.add(s.end.name,this.handleEnd),s.cancel&&this.listeners.add(s.cancel.name,this.handleCancel),this.windowListeners.add(Je.Resize,this.handleCancel),this.windowListeners.add(Je.DragStart,Dh),this.windowListeners.add(Je.VisibilityChange,this.handleCancel),this.windowListeners.add(Je.ContextMenu,Dh),this.documentListeners.add(Je.Keydown,this.handleKeydown),o){if(r!=null&&r({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(wh(o)){this.timeoutId=setTimeout(this.handleStart,o.delay),this.handlePending(o);return}if(zh(o)){this.handlePending(o);return}}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),this.timeoutId!==null&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(s,o){const{active:r,onPending:h}=this.props;h(r,s,this.initialCoordinates,o)}handleStart(){const{initialCoordinates:s}=this,{onStart:o}=this.props;s&&(this.activated=!0,this.documentListeners.add(Je.Click,qp,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(Je.SelectionChange,this.removeTextSelection),o(s))}handleMove(s){var o;const{activated:r,initialCoordinates:h,props:m}=this,{onMove:S,options:{activationConstraint:M}}=m;if(!h)return;const x=(o=mr(s))!=null?o:la,p=Li(h,x);if(!r&&M){if(zh(M)){if(M.tolerance!=null&&rr(p,M.tolerance))return this.handleCancel();if(rr(p,M.distance))return this.handleStart()}if(wh(M)&&rr(p,M.tolerance))return this.handleCancel();this.handlePending(M,p);return}s.cancelable&&s.preventDefault(),S(x)}handleEnd(){const{onAbort:s,onEnd:o}=this.props;this.detach(),this.activated||s(this.props.active),o()}handleCancel(){const{onAbort:s,onCancel:o}=this.props;this.detach(),this.activated||s(this.props.active),o()}handleKeydown(s){s.code===Et.Esc&&this.handleCancel()}removeTextSelection(){var s;(s=this.document.getSelection())==null||s.removeAllRanges()}}const Gp={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class cg extends wr{constructor(s){const{event:o}=s,r=An(o.target);super(s,Gp,r)}}cg.activators=[{eventName:"onPointerDown",handler:(i,s)=>{let{nativeEvent:o}=i,{onActivation:r}=s;return!o.isPrimary||o.button!==0?!1:(r==null||r({event:o}),!0)}}];const Lp={move:{name:"mousemove"},end:{name:"mouseup"}};var pr;(function(i){i[i.RightClick=2]="RightClick"})(pr||(pr={}));class Xp extends wr{constructor(s){super(s,Lp,An(s.event.target))}}Xp.activators=[{eventName:"onMouseDown",handler:(i,s)=>{let{nativeEvent:o}=i,{onActivation:r}=s;return o.button===pr.RightClick?!1:(r==null||r({event:o}),!0)}}];const or={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class Qp extends wr{constructor(s){super(s,or)}static setup(){return window.addEventListener(or.move.name,s,{capture:!1,passive:!1}),function(){window.removeEventListener(or.move.name,s)};function s(){}}}Qp.activators=[{eventName:"onTouchStart",handler:(i,s)=>{let{nativeEvent:o}=i,{onActivation:r}=s;const{touches:h}=o;return h.length>1?!1:(r==null||r({event:o}),!0)}}];var Au;(function(i){i[i.Pointer=0]="Pointer",i[i.DraggableRect=1]="DraggableRect"})(Au||(Au={}));var Qi;(function(i){i[i.TreeOrder=0]="TreeOrder",i[i.ReversedTreeOrder=1]="ReversedTreeOrder"})(Qi||(Qi={}));function Vp(i){let{acceleration:s,activator:o=Au.Pointer,canScroll:r,draggingRect:h,enabled:m,interval:S=5,order:M=Qi.TreeOrder,pointerCoordinates:x,scrollableAncestors:p,scrollableAncestorRects:N,delta:j,threshold:O}=i;const X=Kp({delta:j,disabled:!m}),[G,H]=lp(),tt=w.useRef({x:0,y:0}),ut=w.useRef({x:0,y:0}),nt=w.useMemo(()=>{switch(o){case Au.Pointer:return x?{top:x.y,bottom:x.y,left:x.x,right:x.x}:null;case Au.DraggableRect:return h}},[o,h,x]),dt=w.useRef(null),At=w.useCallback(()=>{const B=dt.current;if(!B)return;const at=tt.current.x*ut.current.x,st=tt.current.y*ut.current.y;B.scrollBy(at,st)},[]),W=w.useMemo(()=>M===Qi.TreeOrder?[...p].reverse():p,[M,p]);w.useEffect(()=>{if(!m||!p.length||!nt){H();return}for(const B of W){if((r==null?void 0:r(B))===!1)continue;const at=p.indexOf(B),st=N[at];if(!st)continue;const{direction:Y,speed:$}=Np(B,st,nt,s,O);for(const pt of["x","y"])X[pt][Y[pt]]||($[pt]=0,Y[pt]=0);if($.x>0||$.y>0){H(),dt.current=B,G(At,S),tt.current=$,ut.current=Y;return}}tt.current={x:0,y:0},ut.current={x:0,y:0},H()},[s,At,r,H,m,S,JSON.stringify(nt),JSON.stringify(X),G,p,W,N,JSON.stringify(O)])}const Zp={x:{[le.Backward]:!1,[le.Forward]:!1},y:{[le.Backward]:!1,[le.Forward]:!1}};function Kp(i){let{delta:s,disabled:o}=i;const r=gr(s);return Du(h=>{if(o||!r||!h)return Zp;const m={x:Math.sign(s.x-r.x),y:Math.sign(s.y-r.y)};return{x:{[le.Backward]:h.x[le.Backward]||m.x===-1,[le.Forward]:h.x[le.Forward]||m.x===1},y:{[le.Backward]:h.y[le.Backward]||m.y===-1,[le.Forward]:h.y[le.Forward]||m.y===1}}},[o,s,r])}function kp(i,s){const o=s!=null?i.get(s):void 0,r=o?o.node.current:null;return Du(h=>{var m;return s==null?null:(m=r??h)!=null?m:null},[r,s])}function Jp(i,s){return w.useMemo(()=>i.reduce((o,r)=>{const{sensor:h}=r,m=h.activators.map(S=>({eventName:S.eventName,handler:s(S.handler,r)}));return[...o,...m]},[]),[i,s])}var Mu;(function(i){i[i.Always=0]="Always",i[i.BeforeDragging=1]="BeforeDragging",i[i.WhileDragging=2]="WhileDragging"})(Mu||(Mu={}));var yr;(function(i){i.Optimized="optimized"})(yr||(yr={}));const Rh=new Map;function $p(i,s){let{dragging:o,dependencies:r,config:h}=s;const[m,S]=w.useState(null),{frequency:M,measure:x,strategy:p}=h,N=w.useRef(i),j=tt(),O=kh(j),X=w.useCallback(function(ut){ut===void 0&&(ut=[]),!O.current&&S(nt=>nt===null?ut:nt.concat(ut.filter(dt=>!nt.includes(dt))))},[O]),G=w.useRef(null),H=Du(ut=>{if(j&&!o)return Rh;if(!ut||ut===Rh||N.current!==i||m!=null){const nt=new Map;for(let dt of i){if(!dt)continue;if(m&&m.length>0&&!m.includes(dt.id)&&dt.rect.current){nt.set(dt.id,dt.rect.current);continue}const At=dt.node.current,W=At?new zr(x(At),At):null;dt.rect.current=W,W&&nt.set(dt.id,W)}return nt}return ut},[i,m,o,j,x]);return w.useEffect(()=>{N.current=i},[i]),w.useEffect(()=>{j||X()},[o,j]),w.useEffect(()=>{m&&m.length>0&&S(null)},[JSON.stringify(m)]),w.useEffect(()=>{j||typeof M!="number"||G.current!==null||(G.current=setTimeout(()=>{X(),G.current=null},M))},[M,j,X,...r]),{droppableRects:H,measureDroppableContainers:X,measuringScheduled:m!=null};function tt(){switch(p){case Mu.Always:return!1;case Mu.BeforeDragging:return o;default:return!o}}}function sg(i,s){return Du(o=>i?o||(typeof s=="function"?s(i):i):null,[s,i])}function Wp(i,s){return sg(i,s)}function Fp(i){let{callback:s,disabled:o}=i;const r=Tr(s),h=w.useMemo(()=>{if(o||typeof window>"u"||typeof window.MutationObserver>"u")return;const{MutationObserver:m}=window;return new m(r)},[r,o]);return w.useEffect(()=>()=>h==null?void 0:h.disconnect(),[h]),h}function Rr(i){let{callback:s,disabled:o}=i;const r=Tr(s),h=w.useMemo(()=>{if(o||typeof window>"u"||typeof window.ResizeObserver>"u")return;const{ResizeObserver:m}=window;return new m(r)},[o]);return w.useEffect(()=>()=>h==null?void 0:h.disconnect(),[h]),h}function Pp(i){return new zr(zu(i),i)}function Oh(i,s,o){s===void 0&&(s=Pp);const[r,h]=w.useState(null);function m(){h(x=>{if(!i)return null;if(i.isConnected===!1){var p;return(p=x??o)!=null?p:null}const N=s(i);return JSON.stringify(x)===JSON.stringify(N)?x:N})}const S=Fp({callback(x){if(i)for(const p of x){const{type:N,target:j}=p;if(N==="childList"&&j instanceof HTMLElement&&j.contains(i)){m();break}}}}),M=Rr({callback:m});return Nl(()=>{m(),i?(M==null||M.observe(i),S==null||S.observe(document.body,{childList:!0,subtree:!0})):(M==null||M.disconnect(),S==null||S.disconnect())},[i]),r}function Ip(i){const s=sg(i);return Ph(i,s)}const Nh=[];function ty(i){const s=w.useRef(i),o=Du(r=>i?r&&r!==Nh&&i&&s.current&&i.parentNode===s.current.parentNode?r:Dr(i):Nh,[i]);return w.useEffect(()=>{s.current=i},[i]),o}function ey(i){const[s,o]=w.useState(null),r=w.useRef(i),h=w.useCallback(m=>{const S=sr(m.target);S&&o(M=>M?(M.set(S,vr(S)),new Map(M)):null)},[]);return w.useEffect(()=>{const m=r.current;if(i!==m){S(m);const M=i.map(x=>{const p=sr(x);return p?(p.addEventListener("scroll",h,{passive:!0}),[p,vr(p)]):null}).filter(x=>x!=null);o(M.length?new Map(M):null),r.current=i}return()=>{S(i),S(m)};function S(M){M.forEach(x=>{const p=sr(x);p==null||p.removeEventListener("scroll",h)})}},[h,i]),w.useMemo(()=>i.length?s?Array.from(s.values()).reduce((m,S)=>yn(m,S),la):ng(i):la,[i,s])}function _h(i,s){s===void 0&&(s=[]);const o=w.useRef(null);return w.useEffect(()=>{o.current=null},s),w.useEffect(()=>{const r=i!==la;r&&!o.current&&(o.current=i),!r&&o.current&&(o.current=null)},[i]),o.current?Li(i,o.current):la}function ay(i){w.useEffect(()=>{if(!Vi)return;const s=i.map(o=>{let{sensor:r}=o;return r.setup==null?void 0:r.setup()});return()=>{for(const o of s)o==null||o()}},i.map(s=>{let{sensor:o}=s;return o}))}function rg(i){return w.useMemo(()=>i?zp(i):null,[i])}const Ch=[];function ly(i,s){s===void 0&&(s=zu);const[o]=i,r=rg(o?Ae(o):null),[h,m]=w.useState(Ch);function S(){m(()=>i.length?i.map(x=>ag(x)?r:new zr(s(x),x)):Ch)}const M=Rr({callback:S});return Nl(()=>{M==null||M.disconnect(),S(),i.forEach(x=>M==null?void 0:M.observe(x))},[i]),h}function ny(i){if(!i)return null;if(i.children.length>1)return i;const s=i.children[0];return Tu(s)?s:i}function uy(i){let{measure:s}=i;const[o,r]=w.useState(null),h=w.useCallback(p=>{for(const{target:N}of p)if(Tu(N)){r(j=>{const O=s(N);return j?{...j,width:O.width,height:O.height}:O});break}},[s]),m=Rr({callback:h}),S=w.useCallback(p=>{const N=ny(p);m==null||m.disconnect(),N&&(m==null||m.observe(N)),r(N?s(N):null)},[s,m]),[M,x]=np(S);return w.useMemo(()=>({nodeRef:M,rect:o,setRef:x}),[o,M,x])}const iy=[{sensor:cg,options:{}},{sensor:ig,options:{}}],cy={current:{}},Gi={draggable:{measure:Th},droppable:{measure:Th,strategy:Mu.WhileDragging,frequency:yr.Optimized},dragOverlay:{measure:zu}};class Eu extends Map{get(s){var o;return s!=null&&(o=super.get(s))!=null?o:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(s=>{let{disabled:o}=s;return!o})}getNodeFor(s){var o,r;return(o=(r=this.get(s))==null?void 0:r.node.current)!=null?o:void 0}}const sy={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new Eu,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:Xi},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:Gi,measureDroppableContainers:Xi,windowRect:null,measuringScheduled:!1},ry={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:Xi,draggableNodes:new Map,over:null,measureDroppableContainers:Xi},og=w.createContext(ry),oy=w.createContext(sy);function fy(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new Eu}}}function dy(i,s){switch(s.type){case oe.DragStart:return{...i,draggable:{...i.draggable,initialCoordinates:s.initialCoordinates,active:s.active}};case oe.DragMove:return i.draggable.active==null?i:{...i,draggable:{...i.draggable,translate:{x:s.coordinates.x-i.draggable.initialCoordinates.x,y:s.coordinates.y-i.draggable.initialCoordinates.y}}};case oe.DragEnd:case oe.DragCancel:return{...i,draggable:{...i.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case oe.RegisterDroppable:{const{element:o}=s,{id:r}=o,h=new Eu(i.droppable.containers);return h.set(r,o),{...i,droppable:{...i.droppable,containers:h}}}case oe.SetDroppableDisabled:{const{id:o,key:r,disabled:h}=s,m=i.droppable.containers.get(o);if(!m||r!==m.key)return i;const S=new Eu(i.droppable.containers);return S.set(o,{...m,disabled:h}),{...i,droppable:{...i.droppable,containers:S}}}case oe.UnregisterDroppable:{const{id:o,key:r}=s,h=i.droppable.containers.get(o);if(!h||r!==h.key)return i;const m=new Eu(i.droppable.containers);return m.delete(o),{...i,droppable:{...i.droppable,containers:m}}}default:return i}}function hy(i){let{disabled:s}=i;const{active:o,activatorEvent:r,draggableNodes:h}=w.useContext(og),m=gr(r),S=gr(o==null?void 0:o.id);return w.useEffect(()=>{if(!s&&!r&&m&&S!=null){if(!Wh(m)||document.activeElement===m.target)return;const M=h.get(S);if(!M)return;const{activatorNode:x,node:p}=M;if(!x.current&&!p.current)return;requestAnimationFrame(()=>{for(const N of[x.current,p.current]){if(!N)continue;const j=cp(N);if(j){j.focus();break}}})}},[r,s,h,S,m]),null}function gy(i,s){let{transform:o,...r}=s;return i!=null&&i.length?i.reduce((h,m)=>m({transform:h,...r}),o):o}function my(i){return w.useMemo(()=>({draggable:{...Gi.draggable,...i==null?void 0:i.draggable},droppable:{...Gi.droppable,...i==null?void 0:i.droppable},dragOverlay:{...Gi.dragOverlay,...i==null?void 0:i.dragOverlay}}),[i==null?void 0:i.draggable,i==null?void 0:i.droppable,i==null?void 0:i.dragOverlay])}function vy(i){let{activeNode:s,measure:o,initialRect:r,config:h=!0}=i;const m=w.useRef(!1),{x:S,y:M}=typeof h=="boolean"?{x:h,y:h}:h;Nl(()=>{if(!S&&!M||!s){m.current=!1;return}if(m.current||!r)return;const p=s==null?void 0:s.node.current;if(!p||p.isConnected===!1)return;const N=o(p),j=Ph(N,r);if(S||(j.x=0),M||(j.y=0),m.current=!0,Math.abs(j.x)>0||Math.abs(j.y)>0){const O=Ih(p);O&&O.scrollBy({top:j.y,left:j.x})}},[s,S,M,r,o])}const py=w.createContext({...la,scaleX:1,scaleY:1});var ll;(function(i){i[i.Uninitialized=0]="Uninitialized",i[i.Initializing=1]="Initializing",i[i.Initialized=2]="Initialized"})(ll||(ll={}));const yy=w.memo(function(s){var o,r,h,m;let{id:S,accessibility:M,autoScroll:x=!0,children:p,sensors:N=iy,collisionDetection:j=xp,measuring:O,modifiers:X,...G}=s;const H=w.useReducer(dy,void 0,fy),[tt,ut]=H,[nt,dt]=hp(),[At,W]=w.useState(ll.Uninitialized),B=At===ll.Initialized,{draggable:{active:at,nodes:st,translate:Y},droppable:{containers:$}}=tt,pt=at!=null?st.get(at):null,_t=w.useRef({initial:null,translated:null}),zt=w.useMemo(()=>{var Lt;return at!=null?{id:at,data:(Lt=pt==null?void 0:pt.data)!=null?Lt:cy,rect:_t}:null},[at,pt]),Mt=w.useRef(null),[Nt,_]=w.useState(null),[F,L]=w.useState(null),lt=kh(G,Object.values(G)),g=Jh("DndDescribedBy",S),U=w.useMemo(()=>$.getEnabled(),[$]),k=my(O),{droppableRects:K,measureDroppableContainers:q,measuringScheduled:ct}=$p(U,{dragging:B,dependencies:[Y.x,Y.y],config:k.droppable}),I=kp(st,at),Ct=w.useMemo(()=>F?mr(F):null,[F]),bt=Ul(),Ut=Wp(I,k.draggable.measure);vy({activeNode:at!=null?st.get(at):null,config:bt.layoutShiftCompensation,initialRect:Ut,measure:k.draggable.measure});const gt=Oh(I,k.draggable.measure,Ut),me=Oh(I?I.parentElement:null),Ee=w.useRef({activatorEvent:null,active:null,activeNode:I,collisionRect:null,collisions:null,droppableRects:K,draggableNodes:st,draggingNode:null,draggingNodeRect:null,droppableContainers:$,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),We=$.getNodeFor((o=Ee.current.over)==null?void 0:o.id),Ce=uy({measure:k.dragOverlay.measure}),ha=(r=Ce.nodeRef.current)!=null?r:I,Me=B?(h=Ce.rect)!=null?h:gt:null,_l=!!(Ce.nodeRef.current&&Ce.rect),nl=Ip(_l?null:gt),ul=rg(ha?Ae(ha):null),Ue=ty(B?We??I:null),_a=ly(Ue),Cl=gy(X,{transform:{x:Y.x-nl.x,y:Y.y-nl.y,scaleX:1,scaleY:1},activatorEvent:F,active:zt,activeNodeRect:gt,containerNodeRect:me,draggingNodeRect:Me,over:Ee.current.over,overlayNodeRect:Ce.rect,scrollableAncestors:Ue,scrollableAncestorRects:_a,windowRect:ul}),wu=Ct?yn(Ct,Y):null,Te=ey(Ue),Zi=_h(Te),Ru=_h(Te,[gt]),ga=yn(Cl,Zi),Fe=Me?Ep(Me,Cl):null,il=zt&&Fe?j({active:zt,collisionRect:Fe,droppableRects:K,droppableContainers:U,pointerCoordinates:wu}):null,En=yp(il,"id"),[na,Ou]=w.useState(null),cl=_l?Cl:yn(Cl,Ru),fe=Sp(cl,(m=na==null?void 0:na.rect)!=null?m:null,gt),Mn=w.useRef(null),Pe=w.useCallback((Lt,Xt)=>{let{sensor:Ft,options:ve}=Xt;if(Mt.current==null)return;const pe=st.get(Mt.current);if(!pe)return;const ce=Lt.nativeEvent,De=new Ft({active:Mt.current,activeNode:pe,event:ce,options:ve,context:Ee,onAbort(jt){if(!st.get(jt))return;const{onDragAbort:ze}=lt.current,je={id:jt};ze==null||ze(je),nt({type:"onDragAbort",event:je})},onPending(jt,ia,ze,je){if(!st.get(jt))return;const{onDragPending:Ua}=lt.current,ca={id:jt,constraint:ia,initialCoordinates:ze,offset:je};Ua==null||Ua(ca),nt({type:"onDragPending",event:ca})},onStart(jt){const ia=Mt.current;if(ia==null)return;const ze=st.get(ia);if(!ze)return;const{onDragStart:je}=lt.current,Ca={activatorEvent:ce,active:{id:ia,data:ze.data,rect:_t}};xu.unstable_batchedUpdates(()=>{je==null||je(Ca),W(ll.Initializing),ut({type:oe.DragStart,initialCoordinates:jt,active:ia}),nt({type:"onDragStart",event:Ca}),_(Mn.current),L(ce)})},onMove(jt){ut({type:oe.DragMove,coordinates:jt})},onEnd:ua(oe.DragEnd),onCancel:ua(oe.DragCancel)});Mn.current=De;function ua(jt){return async function(){const{active:ze,collisions:je,over:Ca,scrollAdjustedTranslate:Ua}=Ee.current;let ca=null;if(ze&&Ua){const{cancelDrop:ye}=lt.current;ca={activatorEvent:ce,active:ze,collisions:je,delta:Ua,over:Ca},jt===oe.DragEnd&&typeof ye=="function"&&await Promise.resolve(ye(ca))&&(jt=oe.DragCancel)}Mt.current=null,xu.unstable_batchedUpdates(()=>{ut({type:jt}),W(ll.Uninitialized),Ou(null),_(null),L(null),Mn.current=null;const ye=jt===oe.DragEnd?"onDragEnd":"onDragCancel";if(ca){const Vt=lt.current[ye];Vt==null||Vt(ca),nt({type:ye,event:ca})}})}}},[st]),de=w.useCallback((Lt,Xt)=>(Ft,ve)=>{const pe=Ft.nativeEvent,ce=st.get(ve);if(Mt.current!==null||!ce||pe.dndKit||pe.defaultPrevented)return;const De={active:ce};Lt(Ft,Xt.options,De)===!0&&(pe.dndKit={capturedBy:Xt.sensor},Mt.current=ve,Pe(Ft,Xt))},[st,Pe]),Nu=Jp(N,de);ay(N),Nl(()=>{gt&&At===ll.Initializing&&W(ll.Initialized)},[gt,At]),w.useEffect(()=>{const{onDragMove:Lt}=lt.current,{active:Xt,activatorEvent:Ft,collisions:ve,over:pe}=Ee.current;if(!Xt||!Ft)return;const ce={active:Xt,activatorEvent:Ft,collisions:ve,delta:{x:ga.x,y:ga.y},over:pe};xu.unstable_batchedUpdates(()=>{Lt==null||Lt(ce),nt({type:"onDragMove",event:ce})})},[ga.x,ga.y]),w.useEffect(()=>{const{active:Lt,activatorEvent:Xt,collisions:Ft,droppableContainers:ve,scrollAdjustedTranslate:pe}=Ee.current;if(!Lt||Mt.current==null||!Xt||!pe)return;const{onDragOver:ce}=lt.current,De=ve.get(En),ua=De&&De.rect.current?{id:De.id,rect:De.rect.current,data:De.data,disabled:De.disabled}:null,jt={active:Lt,activatorEvent:Xt,collisions:Ft,delta:{x:pe.x,y:pe.y},over:ua};xu.unstable_batchedUpdates(()=>{Ou(ua),ce==null||ce(jt),nt({type:"onDragOver",event:jt})})},[En]),Nl(()=>{Ee.current={activatorEvent:F,active:zt,activeNode:I,collisionRect:Fe,collisions:il,droppableRects:K,draggableNodes:st,draggingNode:ha,draggingNodeRect:Me,droppableContainers:$,over:na,scrollableAncestors:Ue,scrollAdjustedTranslate:ga},_t.current={initial:Me,translated:Fe}},[zt,I,il,Fe,st,ha,Me,K,$,na,Ue,ga]),Vp({...bt,delta:Y,draggingRect:Fe,pointerCoordinates:wu,scrollableAncestors:Ue,scrollableAncestorRects:_a});const Ki=w.useMemo(()=>({active:zt,activeNode:I,activeNodeRect:gt,activatorEvent:F,collisions:il,containerNodeRect:me,dragOverlay:Ce,draggableNodes:st,droppableContainers:$,droppableRects:K,over:na,measureDroppableContainers:q,scrollableAncestors:Ue,scrollableAncestorRects:_a,measuringConfiguration:k,measuringScheduled:ct,windowRect:ul}),[zt,I,gt,F,il,me,Ce,st,$,K,na,q,Ue,_a,k,ct,ul]),ki=w.useMemo(()=>({activatorEvent:F,activators:Nu,active:zt,activeNodeRect:gt,ariaDescribedById:{draggable:g},dispatch:ut,draggableNodes:st,over:na,measureDroppableContainers:q}),[F,Nu,zt,gt,ut,g,st,na,q]);return $e.createElement(Fh.Provider,{value:dt},$e.createElement(og.Provider,{value:ki},$e.createElement(oy.Provider,{value:Ki},$e.createElement(py.Provider,{value:fe},p)),$e.createElement(hy,{disabled:(M==null?void 0:M.restoreFocus)===!1})),$e.createElement(vp,{...M,hiddenTextDescribedById:g}));function Ul(){const Lt=(Nt==null?void 0:Nt.autoScrollEnabled)===!1,Xt=typeof x=="object"?x.enabled===!1:x===!1,Ft=B&&!Lt&&!Xt;return typeof x=="object"?{...x,enabled:Ft}:{enabled:Ft}}});function Uh(i,s,o){const r=i.slice();return r.splice(o<0?r.length+o:o,0,r.splice(s,1)[0]),r}Et.Down,Et.Right,Et.Up,Et.Left;var br=(i=>(i.COLLECTION="collection",i.TEMPLATE="template",i.REGULAR="regular",i))(br||{});const by=w.createContext(null);function xy({children:i}){const[s,o]=w.useState({}),[r,h]=w.useState(null),[m,S]=w.useState(null),[M,x]=w.useState({moves:[],currentIndex:-1});w.useEffect(()=>{if(r){const G=r.type===br.TEMPLATE?r.containerId.split("/")[1]:r.containerId;window.parent.postMessage({type:"ELEMENT_MOVED",magicpathId:r.elementMagicId,magicpathPath:r.elementPath,move:{containerId:G,newIndex:r.newIndex,timestamp:r.timestamp,moveId:r.id,type:r.type}},"*"),h(null)}},[r]),w.useEffect(()=>{m&&(window.parent.postMessage({type:"UNDO_ELEMENT_MOVED",magicpathId:m.elementMagicId,magicpathPath:m.elementPath,moveId:m.id},"*"),S(null))},[m]);function p(G,H,tt){const{containerId:ut,elementMagicId:nt,type:dt}=H;if(dt===br.TEMPLATE){const B=ut.split("/")[1],at=Object.keys(G).filter(Y=>Y.includes(B));let st=-1;for(const Y of at){const $=G[Y].indexOf(nt);if($!==-1){st=$;break}}return st===-1?G:at.reduce((Y,$)=>(Y[$]=Uh(G[$],st,tt),Y),{...G})}const At=G[ut]??[],W=At.indexOf(nt);return W===-1?G:{...G,[ut]:Uh(At,W,tt)}}const N=w.useCallback(G=>{const H={...G,id:`move_${Date.now()}_${Math.random().toString(36).slice(2,11)}`,timestamp:Date.now()};o(tt=>p(tt,H,H.newIndex)),x(tt=>{if(tt.moves.some(nt=>nt.id===H.id))return tt;const ut=[...tt.moves.slice(0,tt.currentIndex+1),H];return{moves:ut,currentIndex:ut.length-1}}),h(H)},[o]),j=w.useCallback(()=>{if(M.currentIndex<0)return;const G=M.moves[M.currentIndex];x(H=>({...H,currentIndex:H.currentIndex-1})),o(H=>p(H,G,G.oldIndex)),S(G)},[M,o]),O=w.useCallback(()=>{if(M.currentIndex>=M.moves.length-1)return;const G=M.moves[M.currentIndex+1];x(H=>({...H,currentIndex:H.currentIndex+1})),o(H=>p(H,G,G.newIndex)),h(G)},[M,o]);w.useEffect(()=>{const G=H=>{(H.ctrlKey||H.metaKey)&&H.key==="z"&&!H.shiftKey&&(H.preventDefault(),j()),(H.ctrlKey&&H.key==="y"||H.metaKey&&H.shiftKey&&H.key==="z")&&!H.altKey&&(H.preventDefault(),O())};return document.addEventListener("keydown",G),()=>document.removeEventListener("keydown",G)},[j,O]);const X={order:s,setOrder:o,addMove:N};return z.jsx(by.Provider,{value:X,children:i})}document.documentElement.classList.remove("dark");const Or=()=>{document.documentElement.classList.toggle("dark",!1)};Or();document.addEventListener("DOMContentLoaded",Or);const Sy=window.matchMedia("(prefers-color-scheme: dark)");Sy.addEventListener("change",Or);R0.createRoot(document.getElementById("root")).render(z.jsx(w.StrictMode,{children:z.jsx(xy,{children:z.jsx(yy,{children:z.jsx(ap,{})})})}));
