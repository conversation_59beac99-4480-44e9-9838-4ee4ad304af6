{"name": "component-forge", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@hookform/resolvers": "^4.1.3", "@react-three/drei": "^10.0.6", "@react-three/fiber": "^9.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.4.10", "input-otp": "^1.4.2", "lucide-react": "^0.477.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.1", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "three": "^0.175.0", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.21.0", "@tailwindcss/vite": "^4.0.9", "@types/node": "^22.13.9", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "prettier": "3.5.3", "tailwindcss": "^4.0.9", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}