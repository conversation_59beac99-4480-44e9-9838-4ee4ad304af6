<script lang="ts">
  import { onMount } from "svelte"
  import { slide, fade } from "svelte/transition"
  import {
    Search,
    Target,
    BarChart,
    TrendingUp,
    Sparkles,
    Filter,
    ChevronDown,
    ChevronRight,
    Settings,
    BookOpen,
    Lightbulb,
    X,
  } from "lucide-svelte"

  // Updated for Svelte 5 compatibility
  let {
    collapsed = $bindable(false),
    activeTab = $bindable("niche"),
    onNicheDiscovery = null,
    onGapAnalysis = null,
    onContentCluster = null,
    isLoading = false,
    nicheKeywords = [],
    gapKeywords = [],
    clusterData = []
  }: {
    collapsed?: boolean
    activeTab?: string
    onNicheDiscovery?: ((data: any) => void) | null
    onGapAnalysis?: ((data: any) => void) | null
    onContentCluster?: ((data: any) => void) | null
    isLoading?: boolean
    nicheKeywords?: any[]
    gapKeywords?: any[]
    clusterData?: any[]
  } = $props()

  let expandedSections = $state(new Set(["tools"]))

  const seoTools = [
    {
      id: "niche",
      title: "Niche Discovery",
      description: "Find untapped long-tail keywords",
      icon: Sparkles,
      color: "text-blue-500",
      bgColor: "bg-blue-50",
    },
    {
      id: "gap",
      title: "Gap Analysis",
      description: "Identify competitor keyword gaps",
      icon: BarChart,
      color: "text-green-500",
      bgColor: "bg-green-50",
    },
    {
      id: "cluster",
      title: "Content Clusters",
      description: "Build topic cluster strategies",
      icon: BookOpen,
      color: "text-purple-500",
      bgColor: "bg-purple-50",
    },
  ]

  function toggleSection(sectionId: string) {
    if (expandedSections.has(sectionId)) {
      expandedSections.delete(sectionId)
    } else {
      expandedSections.add(sectionId)
    }
    expandedSections = new Set(expandedSections)
  }

  function selectTool(toolId: string) {
    activeTab = toolId
  }

  function handleNicheAnalysis() {
    if (onNicheDiscovery) {
      onNicheDiscovery({
        seedKeywords: ["example", "keywords"],
        filters: {
          industry: "",
          location: "United States",
          volumeRange: { min: 100, max: 10000 },
          maxDifficulty: 50
        }
      })
    }
  }

  function handleGapAnalysis() {
    if (onGapAnalysis) {
      onGapAnalysis({
        yourDomain: "",
        competitors: [""],
        location: "United States",
        minVolume: 100,
        maxDifficulty: 50,
        gapType: "missing"
      })
    }
  }

  function handleContentCluster() {
    if (onContentCluster) {
      onContentCluster({
        mainTopic: "",
        subtopics: [],
        contentTypes: ["blog", "landing-page"]
      })
    }
  }
</script>

<!-- Right Sidebar -->
<div class="h-full flex flex-col bg-sidebar border-l border-sidebar-border">
  <!-- Header -->
  <div class="p-4 border-b border-sidebar-border">
    <div class="flex items-center justify-between">
      <h2 class="font-semibold text-sidebar-foreground">SEO Tools</h2>
      <button
        onclick={() => collapsed = !collapsed}
        class="p-1 hover:bg-sidebar-accent rounded transition-colors"
      >
        {#if collapsed}
          <ChevronRight class="w-4 h-4 text-muted-foreground" />
        {:else}
          <X class="w-4 h-4 text-muted-foreground" />
        {/if}
      </button>
    </div>
  </div>

  {#if !collapsed}
    <div class="flex-1 overflow-y-auto">
      <!-- SEO Tools Section -->
      <div class="p-4">
        <button
          onclick={() => toggleSection("tools")}
          class="flex items-center justify-between w-full text-left mb-3"
        >
          <span class="font-medium text-sidebar-foreground">Analysis Tools</span>
          {#if expandedSections.has("tools")}
            <ChevronDown class="w-4 h-4 text-muted-foreground" />
          {:else}
            <ChevronRight class="w-4 h-4 text-muted-foreground" />
          {/if}
        </button>

        {#if expandedSections.has("tools")}
          <div class="space-y-2" transition:slide>
            {#each seoTools as tool (tool.id)}
              <button
                onclick={() => selectTool(tool.id)}
                class="w-full p-3 rounded-lg border transition-all text-left {activeTab === tool.id 
                  ? 'border-primary bg-primary/5' 
                  : 'border-border hover:border-primary/50 hover:bg-muted/50'}"
              >
                <div class="flex items-start gap-3">
                  <div class="w-8 h-8 rounded-lg {tool.bgColor} flex items-center justify-center">
                    <svelte:component this={tool.icon} class="w-4 h-4 {tool.color}" />
                  </div>
                  <div class="flex-1 min-w-0">
                    <h3 class="font-medium text-sm text-foreground mb-1">
                      {tool.title}
                    </h3>
                    <p class="text-xs text-muted-foreground">
                      {tool.description}
                    </p>
                  </div>
                </div>
              </button>
            {/each}
          </div>
        {/if}
      </div>

      <!-- Active Tool Content -->
      <div class="border-t border-sidebar-border p-4">
        {#if activeTab === "niche"}
          <!-- Niche Discovery Quick Form -->
          <div class="space-y-4">
            <h3 class="font-medium text-sidebar-foreground">Niche Discovery</h3>
            <div class="space-y-3">
              <div>
                <label class="block text-xs font-medium text-muted-foreground mb-1">
                  Seed Keywords
                </label>
                <textarea
                  placeholder="organic skincare, natural beauty"
                  class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground resize-none"
                  rows="2"
                ></textarea>
              </div>
              <div>
                <label class="block text-xs font-medium text-muted-foreground mb-1">
                  Industry
                </label>
                <input
                  type="text"
                  placeholder="e.g., Beauty & Cosmetics"
                  class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground"
                />
              </div>
              <button
                onclick={handleNicheAnalysis}
                disabled={isLoading}
                class="w-full px-4 py-2 bg-primary text-primary-foreground rounded-lg text-sm font-medium disabled:opacity-50"
              >
                {#if isLoading}
                  Analyzing...
                {:else}
                  Discover Keywords
                {/if}
              </button>
            </div>

            {#if nicheKeywords.length > 0}
              <div class="mt-4 pt-4 border-t border-border">
                <h4 class="text-xs font-medium text-muted-foreground mb-2">
                  Found {nicheKeywords.length} Keywords
                </h4>
                <div class="space-y-2 max-h-40 overflow-y-auto">
                  {#each nicheKeywords.slice(0, 5) as keyword}
                    <div class="p-2 bg-muted/30 rounded text-xs">
                      <div class="font-medium text-foreground">{keyword.keyword}</div>
                      <div class="text-muted-foreground">
                        Vol: {keyword.search_volume} | Diff: {keyword.difficulty}
                      </div>
                    </div>
                  {/each}
                </div>
              </div>
            {/if}
          </div>

        {:else if activeTab === "gap"}
          <!-- Gap Analysis Quick Form -->
          <div class="space-y-4">
            <h3 class="font-medium text-sidebar-foreground">Gap Analysis</h3>
            <div class="space-y-3">
              <div>
                <label class="block text-xs font-medium text-muted-foreground mb-1">
                  Your Domain
                </label>
                <input
                  type="text"
                  placeholder="yourdomain.com"
                  class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground"
                />
              </div>
              <div>
                <label class="block text-xs font-medium text-muted-foreground mb-1">
                  Competitor Domain
                </label>
                <input
                  type="text"
                  placeholder="competitor.com"
                  class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground"
                />
              </div>
              <button
                onclick={handleGapAnalysis}
                disabled={isLoading}
                class="w-full px-4 py-2 bg-primary text-primary-foreground rounded-lg text-sm font-medium disabled:opacity-50"
              >
                {#if isLoading}
                  Analyzing...
                {:else}
                  Find Gaps
                {/if}
              </button>
            </div>

            {#if gapKeywords.length > 0}
              <div class="mt-4 pt-4 border-t border-border">
                <h4 class="text-xs font-medium text-muted-foreground mb-2">
                  Found {gapKeywords.length} Opportunities
                </h4>
                <div class="space-y-2 max-h-40 overflow-y-auto">
                  {#each gapKeywords.slice(0, 5) as keyword}
                    <div class="p-2 bg-muted/30 rounded text-xs">
                      <div class="font-medium text-foreground">{keyword.keyword}</div>
                      <div class="text-muted-foreground">
                        Gap: {keyword.gap_type} | Vol: {keyword.search_volume}
                      </div>
                    </div>
                  {/each}
                </div>
              </div>
            {/if}
          </div>

        {:else if activeTab === "cluster"}
          <!-- Content Cluster Quick Form -->
          <div class="space-y-4">
            <h3 class="font-medium text-sidebar-foreground">Content Clusters</h3>
            <div class="space-y-3">
              <div>
                <label class="block text-xs font-medium text-muted-foreground mb-1">
                  Main Topic
                </label>
                <input
                  type="text"
                  placeholder="e.g., Digital Marketing"
                  class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground"
                />
              </div>
              <div>
                <label class="block text-xs font-medium text-muted-foreground mb-1">
                  Target Audience
                </label>
                <input
                  type="text"
                  placeholder="e.g., Small business owners"
                  class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground"
                />
              </div>
              <button
                onclick={handleContentCluster}
                disabled={isLoading}
                class="w-full px-4 py-2 bg-primary text-primary-foreground rounded-lg text-sm font-medium disabled:opacity-50"
              >
                {#if isLoading}
                  Creating...
                {:else}
                  Build Cluster
                {/if}
              </button>
            </div>

            {#if clusterData.length > 0}
              <div class="mt-4 pt-4 border-t border-border">
                <h4 class="text-xs font-medium text-muted-foreground mb-2">
                  Cluster Strategy
                </h4>
                <div class="space-y-2 max-h-40 overflow-y-auto">
                  {#each clusterData.slice(0, 3) as cluster}
                    <div class="p-2 bg-muted/30 rounded text-xs">
                      <div class="font-medium text-foreground">{cluster.topic}</div>
                      <div class="text-muted-foreground">
                        {cluster.subtopics?.length || 0} subtopics
                      </div>
                    </div>
                  {/each}
                </div>
              </div>
            {/if}
          </div>
        {/if}
      </div>

      <!-- Quick Actions -->
      <div class="border-t border-sidebar-border p-4">
        <h3 class="font-medium text-sidebar-foreground mb-3">Quick Actions</h3>
        <div class="space-y-2">
          <button class="w-full p-2 text-left text-sm text-muted-foreground hover:text-foreground hover:bg-muted/50 rounded transition-colors">
            Export Keywords
          </button>
          <button class="w-full p-2 text-left text-sm text-muted-foreground hover:text-foreground hover:bg-muted/50 rounded transition-colors">
            Save Analysis
          </button>
          <button class="w-full p-2 text-left text-sm text-muted-foreground hover:text-foreground hover:bg-muted/50 rounded transition-colors">
            Share Report
          </button>
        </div>
      </div>
    </div>
  {/if}
</div>
