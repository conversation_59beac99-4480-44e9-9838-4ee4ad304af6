<script lang="ts">
  import { writable } from "svelte/store"
  import { onMount } from "svelte"
  import { slide, fade } from "svelte/transition"
  import {
    Search,
    Target,
    TrendingUp,
    FileText,
    Clock,
    User,
    Bot,
    ChevronRight,
    Sparkles,
    Filter,
    Copy,
    FileDown,
    BarChart,
    BookOpen,
    Lightbulb,
    CheckCircle2,
    Loader2,
    Circle,
    Share2,
  } from "lucide-svelte"
  import { SkeletonLoader, ProgressTracker } from '../shared'

  interface Message {
    id: string
    role: "user" | "assistant"
    content: string
    timestamp: Date
    isReport?: boolean
  }

  interface ProgressStep {
    id: number
    title: string
    description: string
    status: "pending" | "active" | "completed"
    progress?: number
  }

  // Updated for Svelte 5 compatibility
  let {
    messages = writable([]),
    isLoading = false,
    onSendMessage = null,
    progressSteps = [],
    currentProgress = 0,
    envSlug = ""
  }: {
    messages?: any
    isLoading?: boolean
    onSendMessage?: ((message: string, filters?: any) => void) | null
    progressSteps?: ProgressStep[]
    currentProgress?: number
    envSlug?: string
  } = $props()

  let input = $state("")
  let selectedMessageId = $state("")
  let outputFormat = $state("summary")
  let placeholderIndex = $state(0)
  let currentPlaceholder = $state("")
  let showFilters = $state(false)
  let targetAudience = $state("")
  let regionFocus = $state("")
  let funnelStage = $state("awareness")

  // Animated placeholder examples
  const placeholderExamples = [
    "Find long-tail keywords for organic skincare...",
    "Analyze competitor keywords for project management tools...",
    "Discover niche keywords for sustainable fashion brands...",
    "Research local SEO keywords for coffee shops in Seattle...",
  ]

  // Interactive card templates
  const interactiveCards = [
    {
      icon: Search,
      title: "Niche Keyword Discovery",
      description: "Find untapped long-tail keywords in your specific niche",
      prompt:
        "Discover high-value, low-competition keywords for a [Your Niche] business targeting [Your Audience]",
    },
    {
      icon: BarChart,
      title: "Competitor Gap Analysis",
      description: "Identify keyword opportunities your competitors are missing",
      prompt:
        "Analyze keyword gaps between my website and [Competitor Domain] in the [Industry] space",
    },
    {
      icon: TrendingUp,
      title: "Content Cluster Mapping",
      description: "Build topic clusters for better content organization",
      prompt:
        "Create a content cluster strategy for [Main Topic] with supporting subtopics and internal linking",
    },
    {
      icon: Target,
      title: "Local SEO Research",
      description: "Optimize for location-based search queries",
      prompt:
        "Find local SEO opportunities for a [Business Type] in [City, State]",
    },
  ]

  // Placeholder animation
  onMount(() => {
    const interval = setInterval(() => {
      placeholderIndex = (placeholderIndex + 1) % placeholderExamples.length
      currentPlaceholder = placeholderExamples[placeholderIndex]
    }, 3000)

    currentPlaceholder = placeholderExamples[0]

    return () => clearInterval(interval)
  })

  function handleKeyDown(event: KeyboardEvent) {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault()
      sendMessage()
    }
  }

  function sendMessage() {
    if (!input.trim() || isLoading || !onSendMessage) return

    const filters = showFilters ? {
      targetAudience,
      regionFocus,
      funnelStage,
      outputFormat
    } : undefined

    onSendMessage(input.trim(), filters)
    input = ""
  }

  function useTemplate(template: any) {
    input = template.prompt
  }

  function formatTimestamp(date: Date): string {
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
  }

  function copyToClipboard(text: string) {
    navigator.clipboard.writeText(text)
  }

  function downloadReport(content: string, filename: string) {
    const blob = new Blob([content], { type: "text/plain" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = filename
    a.click()
    URL.revokeObjectURL(url)
  }
</script>

<!-- Progress Tracker -->
{#if progressSteps.length > 0}
  <ProgressTracker 
    steps={progressSteps} 
    {currentProgress} 
    isVisible={isLoading}
    title="SEO Analysis Progress"
  />
{/if}

<!-- Messages Area -->
<div class="flex-1 flex flex-col h-full">
  <div class="flex-1 overflow-y-auto p-6">
    {#if $messages.length === 0}
      <!-- Welcome State -->
      <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="text-center mb-12">
          <div class="w-16 h-16 bg-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-6">
            <Target class="w-8 h-8 text-primary" />
          </div>
          <h1 class="text-3xl font-bold text-foreground mb-4">
            SEO Strategy Assistant
          </h1>
          <p class="text-lg text-muted-foreground max-w-2xl mx-auto">
            Discover high-value keywords, analyze competitor gaps, and build content clusters 
            that drive organic traffic to your website.
          </p>
        </div>

        <!-- Interactive Templates -->
        <div class="grid md:grid-cols-2 gap-6 mb-12">
          {#each interactiveCards as card (card.title)}
            <button
              onclick={() => useTemplate(card)}
              class="group p-6 bg-card border border-border rounded-xl hover:border-primary/50 transition-all duration-300 text-left hover:shadow-lg"
            >
              <div class="flex items-start gap-4">
                <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                  <svelte:component this={card.icon} class="w-6 h-6 text-primary" />
                </div>
                <div class="flex-1">
                  <h3 class="font-semibold text-foreground mb-2 group-hover:text-primary transition-colors">
                    {card.title}
                  </h3>
                  <p class="text-sm text-muted-foreground mb-3">
                    {card.description}
                  </p>
                  <div class="text-xs text-muted-foreground bg-muted/50 rounded-lg p-3 font-mono">
                    {card.prompt}
                  </div>
                </div>
              </div>
            </button>
          {/each}
        </div>
      </div>
    {:else}
      <!-- Messages -->
      <div class="max-w-4xl mx-auto space-y-6">
        {#each $messages as message (message.id)}
          <div class="flex gap-4" transition:slide={{ duration: 300 }}>
            <div class="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 {message.role === 'user' ? 'bg-primary' : 'bg-muted'}">
              {#if message.role === "user"}
                <User class="w-4 h-4 text-primary-foreground" />
              {:else}
                <Bot class="w-4 h-4 text-muted-foreground" />
              {/if}
            </div>
            <div class="flex-1 min-w-0">
              <div class="flex items-center gap-2 mb-2">
                <span class="font-medium text-sm text-foreground">
                  {message.role === "user" ? "You" : "SEO Assistant"}
                </span>
                <span class="text-xs text-muted-foreground">
                  {formatTimestamp(message.timestamp)}
                </span>
              </div>
              <div class="prose prose-sm max-w-none text-foreground">
                <div class="whitespace-pre-wrap">{message.content}</div>
              </div>
              {#if message.role === "assistant"}
                <div class="flex items-center gap-2 mt-3">
                  <button
                    onclick={() => copyToClipboard(message.content)}
                    class="text-xs text-muted-foreground hover:text-foreground flex items-center gap-1"
                  >
                    <Copy class="w-3 h-3" />
                    Copy
                  </button>
                  {#if message.isReport}
                    <button
                      onclick={() => downloadReport(message.content, `seo-analysis-${Date.now()}.txt`)}
                      class="text-xs text-muted-foreground hover:text-foreground flex items-center gap-1"
                    >
                      <FileDown class="w-3 h-3" />
                      Download
                    </button>
                  {/if}
                </div>
              {/if}
            </div>
          </div>
        {/each}

        {#if isLoading}
          <SkeletonLoader type="message" count={1} />
        {/if}
      </div>
    {/if}
  </div>

  <!-- Input Area -->
  <div class="border-t border-border p-6">
    <div class="max-w-4xl mx-auto space-y-4">
      <!-- Filters -->
      {#if showFilters}
        <div class="bg-muted/30 rounded-lg p-4" transition:slide>
          <div class="grid md:grid-cols-3 gap-4">
            <div>
              <label class="block text-xs font-bold mb-2 text-foreground">
                Target Audience
              </label>
              <input
                bind:value={targetAudience}
                placeholder="e.g., Small business owners"
                class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground"
              />
            </div>
            <div>
              <label class="block text-xs font-bold mb-2 text-foreground">
                Region Focus
              </label>
              <input
                bind:value={regionFocus}
                placeholder="e.g., United States"
                class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground"
              />
            </div>
            <div>
              <label class="block text-xs font-bold mb-2 text-foreground">
                Funnel Stage
              </label>
              <select
                bind:value={funnelStage}
                class="w-full px-3 py-2 text-sm border border-border rounded-lg bg-background text-foreground"
              >
                <option value="awareness">Awareness</option>
                <option value="consideration">Consideration</option>
                <option value="decision">Decision</option>
              </select>
            </div>
          </div>
        </div>
      {/if}

      <!-- Input -->
      <div class="flex gap-3">
        <button
          onclick={() => showFilters = !showFilters}
          class="px-3 py-2 text-sm border border-border rounded-lg hover:bg-muted transition-colors {showFilters ? 'bg-muted' : ''}"
        >
          <Filter class="w-4 h-4" />
        </button>
        <div class="flex-1 relative">
          <textarea
            bind:value={input}
            onkeydown={handleKeyDown}
            placeholder={currentPlaceholder}
            class="w-full px-4 py-3 border border-border rounded-lg bg-background text-foreground resize-none focus:outline-none focus:ring-2 focus:ring-primary/50"
            rows="2"
            disabled={isLoading}
          ></textarea>
        </div>
        <button
          onclick={sendMessage}
          disabled={!input.trim() || isLoading}
          class="px-6 py-3 bg-primary text-primary-foreground rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
        >
          {#if isLoading}
            <Loader2 class="w-4 h-4 animate-spin" />
          {:else}
            <Target class="w-4 h-4" />
          {/if}
          Analyze
        </button>
      </div>
    </div>
  </div>
</div>
