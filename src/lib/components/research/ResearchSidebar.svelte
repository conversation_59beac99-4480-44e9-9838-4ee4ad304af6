<script lang="ts">
  import { page } from "$app/stores"
  import {
    Search,
    Grid3X3,
    User,
    MessageSquare,
    Calendar,
    Settings,
    ChevronRight,
  } from "lucide-svelte"

  // Props
  export let isOpen = true

  // User data - this would come from your auth system
  const user = {
    name: "<PERSON>",
    email: "<EMAIL>",
    initials: "EC"
  }
</script>

<!-- Left Sidebar -->
<aside 
  class="w-64 bg-sidebar border-r border-sidebar-border flex flex-col transition-all duration-300 ease-in-out"
  class:w-0={!isOpen}
  class:overflow-hidden={!isOpen}
>
  <!-- Header -->
  <header class="p-4 border-b border-sidebar-border">
    <div class="flex items-center space-x-2 mb-4">
      <div class="w-6 h-6 bg-sidebar-primary rounded-sm flex items-center justify-center">
        <Grid3X3 class="w-4 h-4 text-sidebar-primary-foreground" />
      </div>
      <span class="font-semibold text-lg text-sidebar-foreground"><PERSON><PERSON> AI</span>
    </div>
    
    <!-- Search -->
    <div class="relative">
      <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
      <input 
        type="text" 
        placeholder="Search" 
        class="w-full pl-10 pr-8 py-2 bg-sidebar-accent rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sidebar-ring border border-sidebar-border text-sidebar-foreground placeholder:text-muted-foreground" 
      />
      <kbd class="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-muted-foreground">⌘K</kbd>
    </div>
  </header>

  <!-- Navigation -->
  <nav class="flex-1 p-4">
    <div class="space-y-1">
      <button 
        class="flex items-center w-full px-3 py-2 text-sm text-sidebar-primary-foreground bg-sidebar-primary rounded-lg"
      >
        <MessageSquare class="w-4 h-4 mr-3" />
        AI Chat
      </button>
      <button class="flex items-center w-full px-3 py-2 text-sm text-sidebar-foreground hover:bg-sidebar-accent rounded-lg transition-colors">
        <Grid3X3 class="w-4 h-4 mr-3" />
        Projects
      </button>
      <button class="flex items-center w-full px-3 py-2 text-sm text-sidebar-foreground hover:bg-sidebar-accent rounded-lg transition-colors">
        <Grid3X3 class="w-4 h-4 mr-3" />
        Templates
      </button>
      <button class="flex items-center w-full px-3 py-2 text-sm text-sidebar-foreground hover:bg-sidebar-accent rounded-lg transition-colors">
        <Grid3X3 class="w-4 h-4 mr-3" />
        Documents
      </button>
      <button class="flex items-center w-full px-3 py-2 text-sm text-sidebar-foreground hover:bg-sidebar-accent rounded-lg transition-colors">
        <User class="w-4 h-4 mr-3" />
        Community
        <span class="ml-auto bg-primary text-primary-foreground text-xs px-2 py-0.5 rounded-full">NEW</span>
      </button>
      <button class="flex items-center w-full px-3 py-2 text-sm text-sidebar-foreground hover:bg-sidebar-accent rounded-lg transition-colors">
        <Calendar class="w-4 h-4 mr-3" />
        History
      </button>
    </div>
  </nav>

  <!-- Footer -->
  <footer class="p-4 border-t border-sidebar-border">
    <div class="space-y-1">
      <button class="flex items-center w-full px-3 py-2 text-sm text-sidebar-foreground hover:bg-sidebar-accent rounded-lg transition-colors">
        <Settings class="w-4 h-4 mr-3" />
        Settings
      </button>
    </div>
    
    <!-- User Profile -->
    <div class="flex items-center mt-4 pt-4 border-t border-sidebar-border">
      <div class="w-8 h-8 bg-muted rounded-full mr-3 flex items-center justify-center">
        <span class="text-sm font-medium text-muted-foreground">{user.initials}</span>
      </div>
      <div class="flex-1 min-w-0">
        <p class="text-sm font-medium text-sidebar-foreground truncate">{user.name}</p>
        <p class="text-xs text-muted-foreground truncate">{user.email}</p>
      </div>
    </div>
  </footer>
</aside>
