<script lang="ts">
  import { page } from "$app/stores"
  import {
    Search,
    Grid3X3,
    User,
    MessageSquare,
    Calendar,
    Settings,
    ChevronRight,
    Menu,
    X,
  } from "lucide-svelte"

  // Props for collapsible behavior
  export let collapsed = false
  export let isMobile = false

  // Toggle function
  function toggleSidebar() {
    collapsed = !collapsed
  }

  // User data - this would come from your auth system
  const user = {
    name: "Emilia Caitlin",
    email: "<EMAIL>",
    initials: "EC",
  }
</script>

<!-- Left Sidebar -->
<aside
  class="bg-sidebar border-r border-sidebar-border flex flex-col transition-all duration-300 ease-in-out relative"
  class:w-64={!collapsed}
  class:w-16={collapsed && !isMobile}
  class:w-0={collapsed && isMobile}
  class:overflow-hidden={collapsed && isMobile}
>
  <!-- Toggle Button -->
  <button
    on:click={toggleSidebar}
    class="absolute -right-3 top-6 z-10 w-6 h-6 bg-background border border-border rounded-full flex items-center justify-center hover:bg-accent transition-colors"
    class:hidden={isMobile && collapsed}
  >
    {#if collapsed}
      <Menu class="w-3 h-3" />
    {:else}
      <X class="w-3 h-3" />
    {/if}
  </button>
  <!-- Header -->
  <header
    class="p-4 border-b border-sidebar-border"
    class:hidden={collapsed && isMobile}
  >
    <div class="flex items-center space-x-2 mb-4">
      <div
        class="w-6 h-6 bg-sidebar-primary rounded-sm flex items-center justify-center"
      >
        <Grid3X3 class="w-4 h-4 text-sidebar-primary-foreground" />
      </div>
      {#if !collapsed}
        <span class="font-semibold text-lg text-sidebar-foreground"
          >Robynn AI</span
        >
      {/if}
    </div>

    <!-- Search -->
    {#if !collapsed}
      <div class="relative">
        <Search
          class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground"
        />
        <input
          type="text"
          placeholder="Search"
          class="w-full pl-10 pr-8 py-2 bg-sidebar-accent rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sidebar-ring border border-sidebar-border text-sidebar-foreground placeholder:text-muted-foreground"
        />
        <kbd
          class="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-muted-foreground"
          >⌘K</kbd
        >
      </div>
    {:else}
      <!-- Collapsed search icon -->
      <div class="flex justify-center">
        <button class="p-2 hover:bg-sidebar-accent rounded-lg">
          <Search class="w-4 h-4 text-muted-foreground" />
        </button>
      </div>
    {/if}
  </header>

  <!-- Navigation -->
  <nav class="flex-1 p-4">
    <div class="space-y-1">
      <button
        class="flex items-center w-full px-3 py-2 text-sm text-sidebar-primary-foreground bg-sidebar-primary rounded-lg"
        class:justify-center={collapsed}
        title={collapsed ? "AI Chat" : ""}
      >
        <MessageSquare class="w-4 h-4 {!collapsed ? 'mr-3' : ''}" />
        {#if !collapsed}AI Chat{/if}
      </button>
      <button
        class="flex items-center w-full px-3 py-2 text-sm text-sidebar-foreground hover:bg-sidebar-accent rounded-lg transition-colors"
        class:justify-center={collapsed}
        title={collapsed ? "Projects" : ""}
      >
        <Grid3X3 class="w-4 h-4 {!collapsed ? 'mr-3' : ''}" />
        {#if !collapsed}Projects{/if}
      </button>
      <button
        class="flex items-center w-full px-3 py-2 text-sm text-sidebar-foreground hover:bg-sidebar-accent rounded-lg transition-colors"
        class:justify-center={collapsed}
        title={collapsed ? "Templates" : ""}
      >
        <Grid3X3 class="w-4 h-4 {!collapsed ? 'mr-3' : ''}" />
        {#if !collapsed}Templates{/if}
      </button>
      <button
        class="flex items-center w-full px-3 py-2 text-sm text-sidebar-foreground hover:bg-sidebar-accent rounded-lg transition-colors"
        class:justify-center={collapsed}
        title={collapsed ? "Documents" : ""}
      >
        <Grid3X3 class="w-4 h-4 {!collapsed ? 'mr-3' : ''}" />
        {#if !collapsed}Documents{/if}
      </button>
      <button
        class="flex items-center w-full px-3 py-2 text-sm text-sidebar-foreground hover:bg-sidebar-accent rounded-lg transition-colors"
        class:justify-center={collapsed}
        title={collapsed ? "Community" : ""}
      >
        <User class="w-4 h-4 {!collapsed ? 'mr-3' : ''}" />
        {#if !collapsed}
          Community
          <span
            class="ml-auto bg-primary text-primary-foreground text-xs px-2 py-0.5 rounded-full"
            >NEW</span
          >
        {/if}
      </button>
      <button
        class="flex items-center w-full px-3 py-2 text-sm text-sidebar-foreground hover:bg-sidebar-accent rounded-lg transition-colors"
        class:justify-center={collapsed}
        title={collapsed ? "History" : ""}
      >
        <Calendar class="w-4 h-4 {!collapsed ? 'mr-3' : ''}" />
        {#if !collapsed}History{/if}
      </button>
    </div>
  </nav>

  <!-- Footer -->
  <footer
    class="p-4 border-t border-sidebar-border"
    class:hidden={collapsed && isMobile}
  >
    <div class="space-y-1">
      <button
        class="flex items-center w-full px-3 py-2 text-sm text-sidebar-foreground hover:bg-sidebar-accent rounded-lg transition-colors"
        class:justify-center={collapsed}
        title={collapsed ? "Settings" : ""}
      >
        <Settings class="w-4 h-4 {!collapsed ? 'mr-3' : ''}" />
        {#if !collapsed}Settings{/if}
      </button>
    </div>

    <!-- User Profile -->
    <div class="flex items-center mt-4 pt-4 border-t border-sidebar-border">
      <div
        class="w-8 h-8 bg-muted rounded-full flex items-center justify-center"
        class:mr-3={!collapsed}
        class:mx-auto={collapsed}
      >
        <span class="text-sm font-medium text-muted-foreground"
          >{user.initials}</span
        >
      </div>
      {#if !collapsed}
        <div class="flex-1 min-w-0">
          <p class="text-sm font-medium text-sidebar-foreground truncate">
            {user.name}
          </p>
          <p class="text-xs text-muted-foreground truncate">{user.email}</p>
        </div>
      {/if}
    </div>
  </footer>
</aside>
