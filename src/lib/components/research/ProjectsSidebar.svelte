<script lang="ts">
  import { Grid3X3 } from "lucide-svelte"

  // Props
  export let projects = []

  // Default projects data (placeholder)
  const defaultProjects = [
    {
      id: '1',
      title: 'Generate 5 attention-grab...',
      description: '"Revolutionize Customer Enga...'
    },
    {
      id: '2',
      title: 'Learning From 100 Years o...',
      description: 'For athletes, high altitude prod...'
    },
    {
      id: '3',
      title: 'Research officiants',
      description: "<PERSON>'s equations—the foun..."
    },
    {
      id: '4',
      title: 'What does a senior lead de...',
      description: 'Physiological respiration involv...'
    },
    {
      id: '5',
      title: 'Write a sweet note to your...',
      description: 'In the eighteenth century the G...'
    },
    {
      id: '6',
      title: 'Meet with cake bakers',
      description: 'Physical space is often conceiv...'
    },
    {
      id: '7',
      title: 'Meet with cake bakers',
      description: 'Physical space is often conceiv...'
    }
  ]

  // Use provided projects or default ones
  $: displayProjects = projects.length > 0 ? projects : defaultProjects
</script>

<!-- Right Sidebar - Projects -->
<aside class="w-80 bg-background border-l border-border p-6 overflow-y-auto">
  <div class="flex items-center justify-between mb-6">
    <h2 class="text-lg font-semibold text-foreground">Projects ({displayProjects.length})</h2>
    <button class="text-muted-foreground hover:text-foreground transition-colors">
      <Grid3X3 class="w-5 h-5" />
    </button>
  </div>

  <div class="space-y-4">
    {#each displayProjects as project (project.id)}
      <div class="p-4 border border-border rounded-lg hover:bg-accent cursor-pointer transition-colors group">
        <h3 class="font-medium text-sm mb-2 text-foreground group-hover:text-accent-foreground">{project.title}</h3>
        <p class="text-xs text-muted-foreground group-hover:text-accent-foreground/80">{project.description}</p>
      </div>
    {/each}
  </div>

  <!-- Quick Start Templates Section -->
  <div class="mt-8">
    <h3 class="text-md font-semibold text-foreground mb-4">Quick Start Templates</h3>
    <div class="space-y-3">
      <button class="w-full p-3 text-left border border-border rounded-lg hover:bg-accent transition-colors group">
        <div class="text-sm font-medium text-foreground group-hover:text-accent-foreground">Competitive Analysis</div>
        <div class="text-xs text-muted-foreground group-hover:text-accent-foreground/80 mt-1">Analyze competitors' strategies and positioning</div>
      </button>
      
      <button class="w-full p-3 text-left border border-border rounded-lg hover:bg-accent transition-colors group">
        <div class="text-sm font-medium text-foreground group-hover:text-accent-foreground">Market Research</div>
        <div class="text-xs text-muted-foreground group-hover:text-accent-foreground/80 mt-1">Research market trends and opportunities</div>
      </button>
      
      <button class="w-full p-3 text-left border border-border rounded-lg hover:bg-accent transition-colors group">
        <div class="text-sm font-medium text-foreground group-hover:text-accent-foreground">Industry Analysis</div>
        <div class="text-xs text-muted-foreground group-hover:text-accent-foreground/80 mt-1">Deep dive into industry dynamics</div>
      </button>
      
      <button class="w-full p-3 text-left border border-border rounded-lg hover:bg-accent transition-colors group">
        <div class="text-sm font-medium text-foreground group-hover:text-accent-foreground">Customer Research</div>
        <div class="text-xs text-muted-foreground group-hover:text-accent-foreground/80 mt-1">Understand customer needs and behavior</div>
      </button>
    </div>
  </div>
</aside>
