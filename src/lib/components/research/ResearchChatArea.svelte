<script lang="ts">
  import { page } from "$app/stores"
  import { Building2, Zap, ChevronRight } from "lucide-svelte"

  // Props for messages and state
  export let messages = []
  export let isLoading = false
  export let progressSteps = []
  export let currentProgress = 0

  // Temporary placeholder content
  const placeholderMessages = [
    {
      id: "1",
      role: "user",
      content:
        "Generate 5 attention-grabbing headlines for an article about AI Chat Copywriter",
      timestamp: new Date(),
    },
    {
      id: "2",
      role: "assistant",
      content: `Here's the results of 5 attention-grabbing headlines:

1. "Revolutionize Customer Engagement with AI Chat Copywriter"

2. "Unleash the Power of AI Chat Copywriters for Transformative Customer Experiences"

3. "Chatbots on Steroids: Meet the AI Copywriter Transforming Conversations"

4. "From Bland to Brilliant: AI Chat Copywriters Make Brands Conversational Rockstars"

5. "Say Goodbye to Boring Chats: AI Copywriters Elevate Conversational Marketing"`,
      timestamp: new Date(),
    },
  ]

  // Use actual messages if provided, otherwise use placeholder
  $: displayMessages = messages.length > 0 ? messages : placeholderMessages
</script>

<!-- Main Content Area -->
<main class="flex-1 flex flex-col bg-background">
  <!-- Header -->
  <header
    class="flex items-center justify-between p-6 border-b border-border bg-background"
  >
    <div class="flex items-center">
      <!-- Breadcrumb Navigation -->
      <nav
        class="flex items-center space-x-2 text-sm text-muted-foreground mb-2"
      >
        <a
          href="/dashboard/{$page.params.envSlug}"
          class="hover:text-foreground transition-colors"
        >
          Dashboard
        </a>
        <ChevronRight class="w-4 h-4" />
        <span class="text-foreground font-medium">Research Agent</span>
      </nav>
    </div>
  </header>

  <!-- Agent Branding -->
  <div class="px-6 py-4 border-b border-border">
    <div class="flex items-center space-x-4">
      <div
        class="w-12 h-12 flex items-center justify-center border-2 border-border bg-primary"
        style="box-shadow: var(--shadow-sm);"
      >
        <Building2 class="w-6 h-6 text-primary-foreground" />
      </div>
      <div>
        <h1 class="text-3xl font-black flex items-center gap-3 text-foreground">
          <Zap class="w-8 h-8 text-primary" />
          Compass
        </h1>
        <p class="text-muted-foreground">
          AI-powered research assistant for competitive analysis and market
          insights
        </p>
      </div>
    </div>
  </div>

  <!-- Chat Messages Area -->
  <div class="flex-1 overflow-y-auto p-6">
    <div class="max-w-4xl mx-auto space-y-6">
      {#each displayMessages as message (message.id)}
        <div class="flex items-start space-x-4">
          <div
            class="w-8 h-8 rounded-full bg-muted flex items-center justify-center flex-shrink-0"
          >
            {#if message.role === "user"}
              <span class="text-sm font-medium text-muted-foreground">U</span>
            {:else}
              <div class="w-4 h-4 bg-primary rounded-sm"></div>
            {/if}
          </div>
          <div class="flex-1">
            {#if message.role === "user"}
              <div
                class="p-4 border-2 border-border bg-primary"
                style="box-shadow: var(--shadow-sm);"
              >
                <p class="font-medium text-primary-foreground">
                  {message.content}
                </p>
              </div>
            {:else}
              <div
                class="border-2 border-border bg-background"
                style="box-shadow: var(--shadow); border-radius: 0.5rem;"
              >
                <div class="p-4">
                  <p class="text-foreground whitespace-pre-wrap">
                    {message.content}
                  </p>
                </div>
              </div>
            {/if}
          </div>
        </div>
      {/each}

      <!-- Progress Tracking (if active) -->
      {#if isLoading && progressSteps.length > 0}
        <div class="border-2 border-border bg-background p-4 rounded-lg">
          <h3 class="font-semibold text-foreground mb-3">Research Progress</h3>
          <div class="space-y-2">
            {#each progressSteps as step}
              <div class="flex items-center space-x-3">
                <div
                  class="w-4 h-4 rounded-full {step.status === 'completed'
                    ? 'bg-green-500'
                    : step.status === 'active'
                      ? 'bg-primary'
                      : 'bg-muted'}"
                ></div>
                <span
                  class="text-sm {step.status === 'completed'
                    ? 'text-green-600'
                    : 'text-foreground'}">{step.title}</span
                >
              </div>
            {/each}
          </div>
          {#if currentProgress > 0}
            <div class="mt-3">
              <div class="w-full bg-muted rounded-full h-2">
                <div
                  class="bg-primary h-2 rounded-full transition-all duration-300"
                  style="width: {currentProgress}%"
                ></div>
              </div>
              <p class="text-xs text-muted-foreground mt-1">
                {currentProgress}% complete
              </p>
            </div>
          {/if}
        </div>
      {/if}
    </div>
  </div>

  <!-- Input Area -->
  <div class="p-6 border-t border-border bg-background">
    <div class="max-w-4xl mx-auto">
      <div class="relative">
        <textarea
          placeholder="Ask me anything about your research..."
          class="w-full px-4 py-3 pr-32 border border-border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent text-foreground bg-background placeholder:text-muted-foreground"
          rows="1"
          style="min-height: 48px; max-height: 120px;"
        ></textarea>
        <div class="absolute right-2 bottom-2 flex items-center space-x-2">
          <button class="p-2 hover:bg-accent rounded-lg transition-colors">
            <svg
              class="w-4 h-4 text-muted-foreground"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"
              ></path>
            </svg>
          </button>
          <button class="p-2 hover:bg-accent rounded-lg transition-colors">
            <svg
              class="w-4 h-4 text-muted-foreground"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"
              ></path>
            </svg>
          </button>
          <button
            class="p-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
          >
            <svg
              class="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
              ></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex items-center space-x-4 mt-3">
        <button
          class="flex items-center px-3 py-1.5 text-sm text-muted-foreground hover:bg-accent rounded-lg border border-border transition-colors"
        >
          <svg
            class="w-4 h-4 mr-1"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"
            ></path>
          </svg>
          Attach
        </button>
        <button
          class="flex items-center px-3 py-1.5 text-sm text-muted-foreground hover:bg-accent rounded-lg border border-border transition-colors"
        >
          <svg
            class="w-4 h-4 mr-1"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"
            ></path>
          </svg>
          Voice Message
        </button>
        <button
          class="flex items-center px-3 py-1.5 text-sm text-primary-foreground bg-primary hover:bg-primary/90 rounded-lg border border-primary transition-colors"
        >
          <svg
            class="w-4 h-4 mr-1"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.456L3 21l2.456-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"
            ></path>
          </svg>
          Browse Prompts
        </button>
        <div class="ml-auto text-xs text-muted-foreground">0 / 3,000</div>
      </div>

      <!-- Disclaimer -->
      <p class="text-xs text-muted-foreground text-center mt-4">
        Research may generate inaccurate information about companies, markets,
        or facts. Model: Compass AI v1.3
      </p>
    </div>
  </div>
</main>
