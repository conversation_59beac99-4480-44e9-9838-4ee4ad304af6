import { json } from "@sveltejs/kit"
import { createClient } from "@supabase/supabase-js"
import { env } from "$env/dynamic/private"
import { SUPABASE_SERVICE_ROLE_KEY } from "$env/static/private"
import { PUBLIC_SUPABASE_URL } from "$env/static/public"
import type { RequestHandler } from "./$types"

// Create Supabase client with service role for server-side operations
// Use static environment variables (loaded at build time) or dynamic ones
const supabaseUrl = PUBLIC_SUPABASE_URL || env.PUBLIC_SUPABASE_URL
const serviceRoleKey =
  SUPABASE_SERVICE_ROLE_KEY || env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !serviceRoleKey) {
  throw new Error("Missing required Supabase environment variables")
}

const supabase = createClient(supabaseUrl, serviceRoleKey)

interface ContactFormData {
  name: string
  email: string
  website?: string
  description: string
}

export const POST: RequestHandler = async ({ request }) => {
  try {
    const formData: ContactFormData = await request.json()

    // Validate required fields
    if (!formData.name || !formData.email || !formData.description) {
      return json(
        {
          success: false,
          error:
            "Missing required fields: name, email, and description are required",
        },
        { status: 400 },
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(formData.email)) {
      return json(
        {
          success: false,
          error: "Invalid email format",
        },
        { status: 400 },
      )
    }

    // Sanitize and prepare data for contact_requests table
    // Safely extract company name from website URL
    let companyName = ""
    if (formData.website && formData.website.trim()) {
      try {
        const url = new URL(formData.website.trim())
        companyName = url.hostname
      } catch (error) {
        // If URL parsing fails, use the website string as-is (truncated if needed)
        companyName = formData.website.trim().substring(0, 100)
      }
    }

    const submissionData = {
      first_name: formData.name.trim().split(" ")[0] || formData.name.trim(),
      last_name: formData.name.trim().split(" ").slice(1).join(" ") || "",
      email: formData.email.toLowerCase().trim(),
      company_name: companyName,
      phone: "", // Optional field, not provided in modal
      message_body: formData.description.trim(),
    }

    // Insert the contact submission into contact_requests table
    const { data, error } = await supabase
      .from("contact_requests")
      .insert(submissionData)
      .select("id, email, created_at")
      .single()

    if (error) {
      console.error("Supabase error:", error)
      return json(
        {
          success: false,
          error: "Failed to save contact submission",
        },
        { status: 500 },
      )
    }

    return json({
      success: true,
      message: "Contact submission saved successfully",
      data: {
        id: data.id,
        email: data.email,
        created_at: data.created_at,
      },
    })
  } catch (error) {
    console.error("Contact form submission error:", error)
    return json(
      {
        success: false,
        error: "Internal server error",
      },
      { status: 500 },
    )
  }
}
